"""
Enhanced Conversation models with global context support
"""

from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy import Column, Integer, String, DateTime, Boolean, Text, JSON, ForeignKey, Index
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from ..database import Base


class Conversation(Base):
    """Enhanced conversation model with global context support"""
    
    __tablename__ = "conversations"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # User and case associations
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    case_uuid = Column(String(36), ForeignKey("cases.uuid"), nullable=True, index=True)
    
    # Conversation metadata
    title = Column(String(500), nullable=True)
    summary = Column(Text, nullable=True)
    practice_area = Column(String(100), nullable=True, index=True)
    
    # Context and memory
    context_keywords = Column(JSON, nullable=True)  # List of keywords for context matching
    related_conversations = Column(JSON, nullable=True)  # List of related conversation UUIDs
    continuation_from = Column(String(36), nullable=True, index=True)  # Previous conversation UUID
    
    # Status and settings
    is_active = Column(Boolean, default=True)
    is_archived = Column(Boolean, default=False)
    is_pinned = Column(Boolean, default=False)
    
    # Memory settings
    memory_enabled = Column(Boolean, default=True)
    cross_conversation_context = Column(Boolean, default=True)
    max_context_messages = Column(Integer, default=50)
    
    # Analytics
    message_count = Column(Integer, default=0)
    total_tokens_used = Column(Integer, default=0)
    avg_response_time = Column(Integer, default=0)  # milliseconds
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now(), index=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    last_message_at = Column(DateTime, nullable=True, index=True)
    
    # Relationships
    user = relationship("User", back_populates="conversations")
    case = relationship("Case", back_populates="conversations")
    messages = relationship("Message", back_populates="conversation", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_conversation_user_active', 'user_id', 'is_active'),
        Index('idx_conversation_practice_area', 'practice_area', 'is_active'),
        Index('idx_conversation_last_message', 'last_message_at', 'is_active'),
        Index('idx_conversation_case', 'case_uuid', 'is_active'),
    )
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, uuid='{self.uuid}', title='{self.title}')>"
    
    def to_dict(self) -> dict:
        """Convert conversation to dictionary"""
        return {
            "id": self.id,
            "uuid": self.uuid,
            "user_id": self.user_id,
            "case_uuid": self.case_uuid,
            "title": self.title,
            "summary": self.summary,
            "practice_area": self.practice_area,
            "context_keywords": self.context_keywords,
            "related_conversations": self.related_conversations,
            "continuation_from": self.continuation_from,
            "is_active": self.is_active,
            "is_archived": self.is_archived,
            "is_pinned": self.is_pinned,
            "memory_enabled": self.memory_enabled,
            "cross_conversation_context": self.cross_conversation_context,
            "max_context_messages": self.max_context_messages,
            "message_count": self.message_count,
            "total_tokens_used": self.total_tokens_used,
            "avg_response_time": self.avg_response_time,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "last_message_at": self.last_message_at.isoformat() if self.last_message_at else None,
        }


class Message(Base):
    """Enhanced message model with context and embeddings"""
    
    __tablename__ = "messages"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Conversation association
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    
    # Message content
    role = Column(String(20), nullable=False, index=True)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)
    content_type = Column(String(50), default='text')  # 'text', 'file', 'image', etc.
    
    # User association (for user messages)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True, index=True)
    
    # Context and memory
    context_used = Column(JSON, nullable=True)  # Context that was used for this message
    relevant_messages = Column(JSON, nullable=True)  # IDs of relevant previous messages
    confidence_score = Column(Integer, nullable=True)  # AI confidence (0-100)
    
    # Processing metadata
    tokens_used = Column(Integer, nullable=True)
    processing_time = Column(Integer, nullable=True)  # milliseconds
    model_used = Column(String(100), nullable=True)
    
    # File associations
    attached_files = Column(JSON, nullable=True)  # List of file UUIDs
    
    # Message metadata
    metadata = Column(JSON, nullable=True)  # Additional metadata
    
    # Status
    is_edited = Column(Boolean, default=False)
    is_deleted = Column(Boolean, default=False)
    is_flagged = Column(Boolean, default=False)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now(), index=True)
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    conversation = relationship("Conversation", back_populates="messages")
    user = relationship("User")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_message_conversation_created', 'conversation_id', 'created_at'),
        Index('idx_message_role_created', 'role', 'created_at'),
        Index('idx_message_user_created', 'user_id', 'created_at'),
    )
    
    def __repr__(self):
        return f"<Message(id={self.id}, uuid='{self.uuid}', role='{self.role}')>"
    
    def to_dict(self) -> dict:
        """Convert message to dictionary"""
        return {
            "id": self.id,
            "uuid": self.uuid,
            "conversation_id": self.conversation_id,
            "role": self.role,
            "content": self.content,
            "content_type": self.content_type,
            "user_id": self.user_id,
            "context_used": self.context_used,
            "relevant_messages": self.relevant_messages,
            "confidence_score": self.confidence_score,
            "tokens_used": self.tokens_used,
            "processing_time": self.processing_time,
            "model_used": self.model_used,
            "attached_files": self.attached_files,
            "metadata": self.metadata,
            "is_edited": self.is_edited,
            "is_deleted": self.is_deleted,
            "is_flagged": self.is_flagged,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class ConversationSummary(Base):
    """Conversation summaries for efficient context retrieval"""
    
    __tablename__ = "conversation_summaries"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, index=True, default=lambda: str(uuid.uuid4()))
    
    # Conversation association
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Summary content
    title = Column(String(500), nullable=True)
    summary = Column(Text, nullable=False)
    key_points = Column(JSON, nullable=True)  # List of key points
    topics = Column(JSON, nullable=True)  # List of topics discussed
    entities = Column(JSON, nullable=True)  # Named entities (people, places, etc.)
    
    # Context metadata
    practice_areas = Column(JSON, nullable=True)  # List of practice areas
    case_references = Column(JSON, nullable=True)  # Referenced cases
    document_references = Column(JSON, nullable=True)  # Referenced documents
    
    # Summary statistics
    message_count = Column(Integer, default=0)
    date_range_start = Column(DateTime, nullable=True)
    date_range_end = Column(DateTime, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    
    # Relationships
    conversation = relationship("Conversation")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ConversationSummary(id={self.id}, conversation_id={self.conversation_id})>"


class MessageEmbedding(Base):
    """Message embeddings for semantic search"""
    
    __tablename__ = "message_embeddings"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Message association
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Embedding data (stored as JSON for PostgreSQL)
    embedding_vector = Column(JSON, nullable=False)  # Vector embedding
    embedding_model = Column(String(100), nullable=False)  # Model used
    
    # Content metadata
    content_hash = Column(String(64), nullable=False, index=True)  # SHA-256 of content
    content_length = Column(Integer, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    message = relationship("Message")
    conversation = relationship("Conversation")
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index('idx_embedding_conversation', 'conversation_id', 'created_at'),
        Index('idx_embedding_user', 'user_id', 'created_at'),
        Index('idx_embedding_hash', 'content_hash'),
    )
    
    def __repr__(self):
        return f"<MessageEmbedding(id={self.id}, message_id={self.message_id})>"


class ConversationContext(Base):
    """Cross-conversation context relationships"""
    
    __tablename__ = "conversation_contexts"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Conversation associations
    source_conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    target_conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    
    # Relationship metadata
    relationship_type = Column(String(50), nullable=False)  # 'continuation', 'related', 'reference'
    similarity_score = Column(Integer, nullable=True)  # 0-100
    context_strength = Column(String(20), default='medium')  # 'weak', 'medium', 'strong'
    
    # Context data
    shared_topics = Column(JSON, nullable=True)  # Common topics
    shared_entities = Column(JSON, nullable=True)  # Common entities
    
    # Timestamps
    created_at = Column(DateTime, server_default=func.now())
    
    # Relationships
    source_conversation = relationship("Conversation", foreign_keys=[source_conversation_id])
    target_conversation = relationship("Conversation", foreign_keys=[target_conversation_id])
    user = relationship("User")
    
    # Indexes
    __table_args__ = (
        Index('idx_context_source', 'source_conversation_id', 'relationship_type'),
        Index('idx_context_target', 'target_conversation_id', 'relationship_type'),
        Index('idx_context_user', 'user_id', 'relationship_type'),
    )
    
    def __repr__(self):
        return f"<ConversationContext(source={self.source_conversation_id}, target={self.target_conversation_id})>"
