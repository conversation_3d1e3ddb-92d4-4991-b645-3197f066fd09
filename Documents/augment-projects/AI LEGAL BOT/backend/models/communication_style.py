"""
Database models for communication styles and user preferences
"""

from sqlalchemy import <PERSON>um<PERSON>, Integer, String, Text, Float, <PERSON>ole<PERSON>, DateTime, ForeignKey, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from typing import Dict, Any, Optional
import json

from .base import Base


class UserCommunicationStyle(Base):
    """User's communication style preferences"""
    __tablename__ = "user_communication_styles"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Current active style
    active_style_id = Column(String(50), nullable=False, default="advisor")
    
    # Style preferences
    preferred_styles = Column(JSON, default=list)  # List of frequently used style IDs
    style_combinations = Column(JSON, default=dict)  # Custom style combinations
    
    # Context-based style mappings
    context_style_mappings = Column(JSON, default=dict)  # Map contexts to styles
    
    # Customization settings
    custom_prompts = Column(JSO<PERSON>, default=dict)  # Custom prompts for specific styles
    temperature_adjustments = Column(JSON, default=dict)  # Temperature overrides per style
    
    # Usage tracking
    style_usage_count = Column(JSON, default=dict)  # Track usage frequency
    last_style_change = Column(DateTime(timezone=True), server_default=func.now())
    
    # Auto-switching preferences
    auto_switch_enabled = Column(Boolean, default=False)
    auto_switch_rules = Column(JSON, default=list)  # Rules for automatic style switching
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="communication_style")
    
    def get_preferred_styles(self) -> list:
        """Get list of preferred style IDs"""
        return self.preferred_styles or []
    
    def add_preferred_style(self, style_id: str):
        """Add a style to preferred list"""
        preferred = self.get_preferred_styles()
        if style_id not in preferred:
            preferred.append(style_id)
            self.preferred_styles = preferred
    
    def remove_preferred_style(self, style_id: str):
        """Remove a style from preferred list"""
        preferred = self.get_preferred_styles()
        if style_id in preferred:
            preferred.remove(style_id)
            self.preferred_styles = preferred
    
    def get_context_style(self, context: str) -> Optional[str]:
        """Get style for specific context"""
        mappings = self.context_style_mappings or {}
        return mappings.get(context)
    
    def set_context_style(self, context: str, style_id: str):
        """Set style for specific context"""
        mappings = self.context_style_mappings or {}
        mappings[context] = style_id
        self.context_style_mappings = mappings
    
    def increment_style_usage(self, style_id: str):
        """Increment usage count for a style"""
        usage = self.style_usage_count or {}
        usage[style_id] = usage.get(style_id, 0) + 1
        self.style_usage_count = usage
    
    def get_most_used_styles(self, limit: int = 5) -> list:
        """Get most frequently used styles"""
        usage = self.style_usage_count or {}
        sorted_styles = sorted(usage.items(), key=lambda x: x[1], reverse=True)
        return [style_id for style_id, count in sorted_styles[:limit]]


class ConversationStyleHistory(Base):
    """Track style changes within conversations"""
    __tablename__ = "conversation_style_history"
    
    id = Column(Integer, primary_key=True, index=True)
    conversation_id = Column(Integer, ForeignKey("conversations.id"), nullable=False)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Style information
    style_id = Column(String(50), nullable=False)
    style_combination = Column(String(100))  # For combined styles like "litigator_plaintiff"
    
    # Context that triggered the style
    trigger_context = Column(String(100))  # e.g., "case_type_change", "user_request", "auto_switch"
    trigger_message = Column(Text)  # The message that triggered the style change
    
    # Style configuration at time of use
    style_config = Column(JSON)  # Snapshot of style configuration used
    
    # Timing
    started_at = Column(DateTime(timezone=True), server_default=func.now())
    ended_at = Column(DateTime(timezone=True))
    
    # Performance metrics
    user_satisfaction = Column(Integer)  # 1-5 rating if provided
    response_quality = Column(Float)  # Automated quality score
    
    # Relationships
    conversation = relationship("Conversation", back_populates="style_history")
    user = relationship("User")
    
    def get_style_config(self) -> Dict[str, Any]:
        """Get style configuration as dictionary"""
        return self.style_config or {}
    
    def set_style_config(self, config: Dict[str, Any]):
        """Set style configuration"""
        self.style_config = config


class StyleTemplate(Base):
    """Custom style templates created by users or admins"""
    __tablename__ = "style_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Template identification
    template_id = Column(String(50), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=False)
    description = Column(Text)
    
    # Template configuration
    base_style_id = Column(String(50), nullable=False)  # Base style to extend
    custom_system_prompt = Column(Text)
    temperature_override = Column(Float)
    max_tokens_override = Column(Integer)
    
    # Customizations
    additional_tone_keywords = Column(JSON, default=list)
    additional_avoid_keywords = Column(JSON, default=list)
    custom_example_phrases = Column(JSON, default=list)
    context_adaptations = Column(JSON, default=dict)
    
    # Ownership and sharing
    created_by_user_id = Column(Integer, ForeignKey("users.id"))
    is_public = Column(Boolean, default=False)
    is_system_template = Column(Boolean, default=False)
    
    # Usage tracking
    usage_count = Column(Integer, default=0)
    average_rating = Column(Float)
    
    # Metadata
    tags = Column(JSON, default=list)  # Tags for categorization
    practice_areas = Column(JSON, default=list)  # Applicable practice areas
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    created_by = relationship("User")
    
    def get_tags(self) -> list:
        """Get template tags"""
        return self.tags or []
    
    def add_tag(self, tag: str):
        """Add a tag to the template"""
        tags = self.get_tags()
        if tag not in tags:
            tags.append(tag)
            self.tags = tags
    
    def get_practice_areas(self) -> list:
        """Get applicable practice areas"""
        return self.practice_areas or []
    
    def increment_usage(self):
        """Increment usage counter"""
        self.usage_count = (self.usage_count or 0) + 1


class StyleAnalytics(Base):
    """Analytics for style usage and effectiveness"""
    __tablename__ = "style_analytics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Time period
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    period_type = Column(String(20), nullable=False)  # 'daily', 'weekly', 'monthly'
    
    # Style metrics
    style_id = Column(String(50), nullable=False, index=True)
    usage_count = Column(Integer, default=0)
    unique_users = Column(Integer, default=0)
    
    # Performance metrics
    average_response_time = Column(Float)
    average_user_satisfaction = Column(Float)
    average_response_quality = Column(Float)
    
    # Context analysis
    top_contexts = Column(JSON, default=list)  # Most common contexts for this style
    context_distribution = Column(JSON, default=dict)  # Distribution across contexts
    
    # User behavior
    style_switches_from = Column(JSON, default=dict)  # What styles users switch from
    style_switches_to = Column(JSON, default=dict)  # What styles users switch to
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
