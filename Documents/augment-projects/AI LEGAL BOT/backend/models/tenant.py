"""
Multi-tenant models for Legal AI Bot deployment system
"""

from sqlalchemy import Column, Integer, String, DateTime, Boolean, JSON, Text, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
import enum

from ..database import Base


class DeploymentStatus(enum.Enum):
    PENDING = "pending"
    PROVISIONING = "provisioning"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"
    ERROR = "error"


class SubscriptionTier(enum.Enum):
    TRIAL = "trial"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


class Tenant(Base):
    """Tenant model for multi-tenant architecture"""
    __tablename__ = "tenants"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, index=True, nullable=False)
    name = Column(String(255), nullable=False)
    domain = Column(String(255), unique=True, index=True, nullable=False)
    status = Column(SQLEnum(DeploymentStatus), default=DeploymentStatus.PENDING, nullable=False)
    subscription_tier = Column(SQLEnum(SubscriptionTier), default=SubscriptionTier.TRIAL, nullable=False)
    
    # Metadata
    settings = Column(JSON, nullable=True)
    custom_branding = Column(JSON, nullable=True)
    features = Column(JSON, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    users = relationship("User", back_populates="tenant")
    subscriptions = relationship("TenantSubscription", back_populates="tenant")
    deployments = relationship("DeploymentInstance", back_populates="tenant")


class TenantSubscription(Base):
    """Tenant subscription management"""
    __tablename__ = "tenant_subscriptions"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    tier = Column(SQLEnum(SubscriptionTier), nullable=False)
    status = Column(String(50), default="active", nullable=False)
    
    # Billing
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    billing_cycle = Column(String(20), default="monthly", nullable=False)  # monthly, yearly
    amount = Column(Integer, nullable=True)  # in cents
    currency = Column(String(3), default="USD", nullable=False)
    
    # Usage limits
    max_users = Column(Integer, nullable=True)
    max_storage_gb = Column(Integer, nullable=True)
    max_api_calls_per_month = Column(Integer, nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationships
    tenant = relationship("Tenant", back_populates="subscriptions")


class DeploymentInstance(Base):
    """Deployment instance tracking"""
    __tablename__ = "deployment_instances"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(String(36), unique=True, index=True, nullable=False)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    
    # Deployment details
    region = Column(String(50), nullable=False)
    status = Column(SQLEnum(DeploymentStatus), nullable=False)
    deployment_id = Column(String(255), nullable=True)  # K8s deployment ID
    namespace = Column(String(255), nullable=True)
    endpoint = Column(String(500), nullable=True)
    
    # Resources
    resources = Column(JSON, nullable=True)  # CPU, memory, storage allocation
    features = Column(JSON, nullable=True)  # Enabled features
    
    # Configuration
    custom_domain = Column(String(255), nullable=True)
    ssl_enabled = Column(Boolean, default=True, nullable=False)
    backup_enabled = Column(Boolean, default=True, nullable=False)
    monitoring_enabled = Column(Boolean, default=True, nullable=False)
    
    # Status tracking
    last_health_check = Column(DateTime(timezone=True), nullable=True)
    health_status = Column(String(50), nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Suspension tracking
    suspension_reason = Column(Text, nullable=True)
    suspended_at = Column(DateTime(timezone=True), nullable=True)
    suspended_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    deployed_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Relationships
    tenant = relationship("Tenant", back_populates="deployments")


class DeploymentLog(Base):
    """Deployment operation logs"""
    __tablename__ = "deployment_logs"

    id = Column(Integer, primary_key=True, index=True)
    deployment_id = Column(Integer, ForeignKey("deployment_instances.id"), nullable=False)
    
    # Log details
    operation = Column(String(100), nullable=False)  # deploy, scale, suspend, etc.
    status = Column(String(50), nullable=False)  # success, failed, in_progress
    message = Column(Text, nullable=True)
    details = Column(JSON, nullable=True)
    
    # Timing
    started_at = Column(DateTime(timezone=True), nullable=False)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    duration_seconds = Column(Integer, nullable=True)
    
    # Audit
    initiated_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)


class UsageMetrics(Base):
    """Usage metrics for billing and monitoring"""
    __tablename__ = "usage_metrics"

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey("tenants.id"), nullable=False)
    deployment_id = Column(Integer, ForeignKey("deployment_instances.id"), nullable=True)
    
    # Metrics
    metric_type = Column(String(100), nullable=False)  # api_calls, storage_used, users_active
    value = Column(Integer, nullable=False)
    unit = Column(String(50), nullable=False)  # count, bytes, minutes
    
    # Time period
    period_start = Column(DateTime(timezone=True), nullable=False)
    period_end = Column(DateTime(timezone=True), nullable=False)
    
    # Metadata
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)


class RegionCapacity(Base):
    """Regional capacity tracking"""
    __tablename__ = "region_capacity"

    id = Column(Integer, primary_key=True, index=True)
    region = Column(String(50), unique=True, nullable=False)
    
    # Capacity limits
    max_cpu_cores = Column(Integer, nullable=False)
    max_memory_gb = Column(Integer, nullable=False)
    max_storage_gb = Column(Integer, nullable=False)
    max_deployments = Column(Integer, nullable=False)
    
    # Current usage
    used_cpu_cores = Column(Integer, default=0, nullable=False)
    used_memory_gb = Column(Integer, default=0, nullable=False)
    used_storage_gb = Column(Integer, default=0, nullable=False)
    active_deployments = Column(Integer, default=0, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    maintenance_mode = Column(Boolean, default=False, nullable=False)
    
    # Metadata
    endpoint = Column(String(500), nullable=False)
    description = Column(Text, nullable=True)
    
    # Audit
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
