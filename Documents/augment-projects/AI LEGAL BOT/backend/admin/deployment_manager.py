"""
Deployment Management System for Legal AI Bot
Handles nationwide deployment, user provisioning, and system administration
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import json
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func, update
from fastapi import HTTPException, status
import httpx

from ..database import get_db
from ..models.user import User
from ..core.security import create_access_token, hash_password

logger = logging.getLogger(__name__)


class DeploymentStatus(Enum):
    PENDING = "pending"
    PROVISIONING = "provisioning"
    ACTIVE = "active"
    SUSPENDED = "suspended"
    TERMINATED = "terminated"
    ERROR = "error"


class SubscriptionTier(Enum):
    TRIAL = "trial"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    CUSTOM = "custom"


@dataclass
class DeploymentConfig:
    """Deployment configuration for a tenant"""
    tenant_id: str
    subscription_tier: SubscriptionTier
    region: str
    resources: Dict[str, Any]
    features: List[str]
    custom_domain: Optional[str] = None
    ssl_enabled: bool = True
    backup_enabled: bool = True
    monitoring_enabled: bool = True


@dataclass
class UserProvisioningRequest:
    """User provisioning request"""
    email: str
    full_name: str
    law_firm: Optional[str] = None
    bar_number: Optional[str] = None
    jurisdiction: Optional[str] = None
    practice_areas: List[str] = None
    subscription_tier: SubscriptionTier = SubscriptionTier.TRIAL
    auto_deploy: bool = True
    welcome_email: bool = True


class DeploymentManager:
    """Manages deployment of Legal AI Bot instances across the USA"""
    
    def __init__(self):
        self.docker_client = None
        self.k8s_client = None
        self.regions = {
            'us-east-1': {'name': 'US East (Virginia)', 'endpoint': 'https://us-east-1.legalai.pro'},
            'us-west-1': {'name': 'US West (California)', 'endpoint': 'https://us-west-1.legalai.pro'},
            'us-central-1': {'name': 'US Central (Texas)', 'endpoint': 'https://us-central-1.legalai.pro'},
            'us-south-1': {'name': 'US South (Florida)', 'endpoint': 'https://us-south-1.legalai.pro'}
        }
        self.deployment_templates = {
            SubscriptionTier.TRIAL: {
                'cpu': '0.5',
                'memory': '1Gi',
                'storage': '10Gi',
                'max_users': 1,
                'features': ['basic_chat', 'file_upload', 'basic_memory']
            },
            SubscriptionTier.PROFESSIONAL: {
                'cpu': '2',
                'memory': '4Gi',
                'storage': '100Gi',
                'max_users': 10,
                'features': ['advanced_chat', 'large_file_upload', 'full_memory', 'case_management']
            },
            SubscriptionTier.ENTERPRISE: {
                'cpu': '8',
                'memory': '16Gi',
                'storage': '1Ti',
                'max_users': 100,
                'features': ['all_features', 'custom_integrations', 'priority_support', 'sso']
            }
        }
    
    async def initialize(self):
        """Initialize deployment manager"""
        try:
            # Initialize Docker client
            self.docker_client = docker.from_env()
            
            # Initialize Kubernetes client
            try:
                config.load_incluster_config()
            except:
                config.load_kube_config()
            self.k8s_client = client.ApiClient()
            
            logger.info("Deployment manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize deployment manager: {str(e)}")
            raise
    
    async def provision_user(
        self, 
        request: UserProvisioningRequest,
        admin_user_id: int
    ) -> Dict[str, Any]:
        """Provision a new user with automatic deployment"""
        try:
            async with get_db() as db:
                # Check if user already exists
                existing_user = await db.execute(
                    select(User).where(User.email == request.email)
                )
                if existing_user.scalar_one_or_none():
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="User already exists"
                    )
                
                # Create tenant
                tenant = Tenant(
                    uuid=str(uuid.uuid4()),
                    name=request.law_firm or f"{request.full_name}'s Practice",
                    domain=self._generate_tenant_domain(request.email),
                    status=DeploymentStatus.PENDING.value,
                    subscription_tier=request.subscription_tier.value,
                    created_by=admin_user_id
                )
                db.add(tenant)
                await db.flush()
                
                # Create user
                user = User(
                    email=request.email,
                    full_name=request.full_name,
                    password_hash=hash_password(self._generate_temp_password()),
                    law_firm=request.law_firm,
                    bar_number=request.bar_number,
                    jurisdiction=request.jurisdiction,
                    practice_areas=request.practice_areas or [],
                    tenant_id=tenant.id,
                    is_active=True,
                    subscription_tier=request.subscription_tier.value
                )
                db.add(user)
                await db.flush()
                
                # Create subscription
                subscription = TenantSubscription(
                    tenant_id=tenant.id,
                    tier=request.subscription_tier.value,
                    status='active',
                    start_date=datetime.utcnow(),
                    end_date=datetime.utcnow() + timedelta(days=30 if request.subscription_tier == SubscriptionTier.TRIAL else 365)
                )
                db.add(subscription)
                
                await db.commit()
                
                # Auto-deploy if requested
                deployment_result = None
                if request.auto_deploy:
                    deployment_config = DeploymentConfig(
                        tenant_id=tenant.uuid,
                        subscription_tier=request.subscription_tier,
                        region=self._select_optimal_region(request.jurisdiction),
                        resources=self.deployment_templates[request.subscription_tier],
                        features=self.deployment_templates[request.subscription_tier]['features']
                    )
                    
                    deployment_result = await self.deploy_instance(deployment_config)
                
                # Send welcome email
                if request.welcome_email:
                    await send_welcome_email(
                        user.email,
                        user.full_name,
                        tenant.domain,
                        self._generate_temp_password()
                    )
                
                return {
                    'user_id': user.id,
                    'tenant_id': tenant.uuid,
                    'domain': tenant.domain,
                    'subscription_tier': request.subscription_tier.value,
                    'deployment': deployment_result,
                    'status': 'provisioned'
                }
                
        except Exception as e:
            logger.error(f"Failed to provision user: {str(e)}")
            raise
    
    async def deploy_instance(self, config: DeploymentConfig) -> Dict[str, Any]:
        """Deploy a new Legal AI Bot instance"""
        try:
            async with get_db() as db:
                # Create deployment record
                deployment = DeploymentInstance(
                    uuid=str(uuid.uuid4()),
                    tenant_id=config.tenant_id,
                    region=config.region,
                    status=DeploymentStatus.PROVISIONING.value,
                    resources=config.resources,
                    features=config.features,
                    endpoint=f"https://{config.tenant_id}.{config.region}.legalai.pro"
                )
                db.add(deployment)
                await db.commit()
                
                # Deploy to Kubernetes
                deployment_result = await self._deploy_to_kubernetes(config, deployment.uuid)
                
                # Update deployment status
                deployment.status = DeploymentStatus.ACTIVE.value if deployment_result['success'] else DeploymentStatus.ERROR.value
                deployment.deployment_id = deployment_result.get('deployment_id')
                deployment.endpoint = deployment_result.get('endpoint')
                await db.commit()
                
                # Send deployment notification
                await send_deployment_notification(
                    config.tenant_id,
                    deployment.status,
                    deployment.endpoint
                )
                
                return {
                    'deployment_id': deployment.uuid,
                    'status': deployment.status,
                    'endpoint': deployment.endpoint,
                    'region': config.region,
                    'resources': config.resources
                }
                
        except Exception as e:
            logger.error(f"Failed to deploy instance: {str(e)}")
            raise
    
    async def scale_deployment(
        self,
        tenant_id: str,
        new_tier: SubscriptionTier,
        admin_user_id: int
    ) -> Dict[str, Any]:
        """Scale deployment to new subscription tier"""
        try:
            async with get_db() as db:
                # Get current deployment
                deployment = await db.execute(
                    select(DeploymentInstance).where(
                        DeploymentInstance.tenant_id == tenant_id,
                        DeploymentInstance.status == DeploymentStatus.ACTIVE.value
                    )
                )
                deployment = deployment.scalar_one_or_none()
                
                if not deployment:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Active deployment not found"
                    )
                
                # Update resources
                new_resources = self.deployment_templates[new_tier]
                
                # Scale Kubernetes deployment
                scale_result = await self._scale_kubernetes_deployment(
                    deployment.deployment_id,
                    new_resources
                )
                
                if scale_result['success']:
                    # Update deployment record
                    deployment.resources = new_resources
                    deployment.features = new_resources['features']
                    deployment.updated_at = datetime.utcnow()
                    
                    # Update tenant subscription
                    await db.execute(
                        update(TenantSubscription)
                        .where(TenantSubscription.tenant_id == deployment.tenant_id)
                        .values(tier=new_tier.value, updated_at=datetime.utcnow())
                    )
                    
                    await db.commit()
                
                return {
                    'deployment_id': deployment.uuid,
                    'old_tier': deployment.resources,
                    'new_tier': new_resources,
                    'status': 'scaled' if scale_result['success'] else 'failed'
                }
                
        except Exception as e:
            logger.error(f"Failed to scale deployment: {str(e)}")
            raise
    
    async def suspend_deployment(self, tenant_id: str, reason: str) -> Dict[str, Any]:
        """Suspend a deployment"""
        try:
            async with get_db() as db:
                # Get deployment
                deployment = await db.execute(
                    select(DeploymentInstance).where(
                        DeploymentInstance.tenant_id == tenant_id,
                        DeploymentInstance.status == DeploymentStatus.ACTIVE.value
                    )
                )
                deployment = deployment.scalar_one_or_none()
                
                if not deployment:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Active deployment not found"
                    )
                
                # Suspend Kubernetes deployment
                suspend_result = await self._suspend_kubernetes_deployment(deployment.deployment_id)
                
                if suspend_result['success']:
                    deployment.status = DeploymentStatus.SUSPENDED.value
                    deployment.suspension_reason = reason
                    deployment.suspended_at = datetime.utcnow()
                    await db.commit()
                
                return {
                    'deployment_id': deployment.uuid,
                    'status': 'suspended' if suspend_result['success'] else 'failed',
                    'reason': reason
                }
                
        except Exception as e:
            logger.error(f"Failed to suspend deployment: {str(e)}")
            raise
    
    async def get_deployment_stats(self) -> Dict[str, Any]:
        """Get deployment statistics"""
        try:
            async with get_db() as db:
                # Total deployments by status
                status_stats = await db.execute(
                    select(
                        DeploymentInstance.status,
                        func.count(DeploymentInstance.id).label('count')
                    ).group_by(DeploymentInstance.status)
                )
                
                # Regional distribution
                region_stats = await db.execute(
                    select(
                        DeploymentInstance.region,
                        func.count(DeploymentInstance.id).label('count')
                    ).group_by(DeploymentInstance.region)
                )
                
                # Subscription tier distribution
                tier_stats = await db.execute(
                    select(
                        TenantSubscription.tier,
                        func.count(TenantSubscription.id).label('count')
                    ).group_by(TenantSubscription.tier)
                )
                
                # Resource utilization
                resource_stats = await self._get_resource_utilization()
                
                return {
                    'total_deployments': sum([row.count for row in status_stats]),
                    'status_distribution': {row.status: row.count for row in status_stats},
                    'regional_distribution': {row.region: row.count for row in region_stats},
                    'tier_distribution': {row.tier: row.count for row in tier_stats},
                    'resource_utilization': resource_stats,
                    'regions': self.regions
                }
                
        except Exception as e:
            logger.error(f"Failed to get deployment stats: {str(e)}")
            raise
    
    async def bulk_provision_users(
        self,
        users: List[UserProvisioningRequest],
        admin_user_id: int
    ) -> Dict[str, Any]:
        """Bulk provision multiple users"""
        results = {
            'successful': [],
            'failed': [],
            'total': len(users)
        }
        
        for user_request in users:
            try:
                result = await self.provision_user(user_request, admin_user_id)
                results['successful'].append({
                    'email': user_request.email,
                    'result': result
                })
            except Exception as e:
                results['failed'].append({
                    'email': user_request.email,
                    'error': str(e)
                })
        
        return results
    
    def _generate_tenant_domain(self, email: str) -> str:
        """Generate tenant domain from email"""
        username = email.split('@')[0]
        # Clean and format username
        clean_username = ''.join(c for c in username if c.isalnum() or c in '-_').lower()
        return f"{clean_username}.legalai.pro"
    
    def _generate_temp_password(self) -> str:
        """Generate temporary password"""
        import secrets
        import string
        alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
        return ''.join(secrets.choice(alphabet) for _ in range(12))
    
    def _select_optimal_region(self, jurisdiction: Optional[str]) -> str:
        """Select optimal region based on jurisdiction"""
        if not jurisdiction:
            return 'us-east-1'  # Default
        
        # Simple mapping - can be enhanced
        region_mapping = {
            'CA': 'us-west-1',
            'TX': 'us-central-1',
            'FL': 'us-south-1',
            'NY': 'us-east-1'
        }
        
        return region_mapping.get(jurisdiction.upper(), 'us-east-1')
    
    async def _deploy_to_kubernetes(self, config: DeploymentConfig, deployment_id: str) -> Dict[str, Any]:
        """Deploy to Kubernetes cluster"""
        try:
            # This would contain actual Kubernetes deployment logic
            # For now, return a mock successful deployment
            
            endpoint = f"https://{config.tenant_id}.{config.region}.legalai.pro"
            
            return {
                'success': True,
                'deployment_id': f"legal-ai-{deployment_id}",
                'endpoint': endpoint,
                'namespace': f"tenant-{config.tenant_id}"
            }
            
        except Exception as e:
            logger.error(f"Kubernetes deployment failed: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def _scale_kubernetes_deployment(self, deployment_id: str, new_resources: Dict[str, Any]) -> Dict[str, Any]:
        """Scale Kubernetes deployment"""
        try:
            # Kubernetes scaling logic would go here
            return {'success': True}
        except Exception as e:
            logger.error(f"Kubernetes scaling failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def _suspend_kubernetes_deployment(self, deployment_id: str) -> Dict[str, Any]:
        """Suspend Kubernetes deployment"""
        try:
            # Kubernetes suspension logic would go here
            return {'success': True}
        except Exception as e:
            logger.error(f"Kubernetes suspension failed: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    async def _get_resource_utilization(self) -> Dict[str, Any]:
        """Get resource utilization across all deployments"""
        try:
            # This would query Kubernetes metrics
            return {
                'cpu_utilization': 65.5,
                'memory_utilization': 72.3,
                'storage_utilization': 45.8,
                'network_utilization': 23.1
            }
        except Exception as e:
            logger.error(f"Failed to get resource utilization: {str(e)}")
            return {}


# Global deployment manager instance
deployment_manager = DeploymentManager()
