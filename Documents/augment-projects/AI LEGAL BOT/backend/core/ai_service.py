"""
Enhanced AI Service with Communication Styles Integration
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import json

from .deepseek_client import DeepSeekClient
from .memory_manager import MemoryManager
from .file_processor import FileProcessor
from .communication_styles import legal_styles
from ..models.conversation import Conversation, Message
from ..models.user import User
from ..models.communication_style import UserCommunicationStyle, ConversationStyleHistory

logger = logging.getLogger(__name__)


class AIService:
    """Enhanced AI service with communication styles support"""
    
    def __init__(self):
        self.deepseek_client = DeepSeekClient()
        self.memory_manager = MemoryManager()
        self.file_processor = FileProcessor()
        self.user_styles: Dict[int, str] = {}  # Cache user active styles
        
    async def generate_response(
        self,
        user_message: str,
        user_id: int,
        conversation_id: int,
        context: Optional[Dict[str, Any]] = None,
        files: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Generate AI response with style-aware processing"""
        try:
            # Get user's active communication style
            user_style_id = await self.get_user_active_style(user_id)
            style = legal_styles.get_style(user_style_id)
            
            if not style:
                logger.warning(f"Style {user_style_id} not found, using default advisor style")
                style = legal_styles.get_style("advisor")
            
            # Process files if provided
            file_context = ""
            if files:
                file_context = await self.file_processor.process_files(files)
            
            # Get conversation memory and context
            memory_context = await self.memory_manager.get_conversation_context(
                conversation_id, user_id
            )
            
            # Build style-aware system prompt
            system_prompt = self.build_style_aware_prompt(
                style, context, memory_context, file_context
            )
            
            # Generate response using DeepSeek with style configuration
            response = await self.deepseek_client.generate_response(
                user_message,
                system_prompt=system_prompt,
                temperature=style.temperature,
                max_tokens=style.max_tokens,
                conversation_history=memory_context.get("recent_messages", [])
            )
            
            # Post-process response based on style
            processed_response = self.post_process_response(response, style)
            
            # Store interaction in memory
            await self.memory_manager.store_interaction(
                conversation_id=conversation_id,
                user_id=user_id,
                user_message=user_message,
                ai_response=processed_response,
                style_id=style.id,
                context=context
            )
            
            return {
                "response": processed_response,
                "style_used": {
                    "id": style.id,
                    "name": style.name,
                    "category": style.category.value
                },
                "context_used": memory_context,
                "confidence_score": self.calculate_confidence_score(processed_response, style)
            }
            
        except Exception as e:
            logger.error(f"Error generating response: {str(e)}")
            return {
                "response": "I apologize, but I encountered an error processing your request. Please try again.",
                "error": str(e),
                "style_used": {"id": "advisor", "name": "Legal Advisor", "category": "professional_role"}
            }
    
    def build_style_aware_prompt(
        self,
        style,
        context: Optional[Dict[str, Any]],
        memory_context: Dict[str, Any],
        file_context: str
    ) -> str:
        """Build system prompt incorporating communication style"""
        
        # Base legal AI prompt
        base_prompt = """You are a professional legal AI assistant with zero tolerance for hallucination. 
        You must provide accurate, well-researched legal information while clearly stating when you cannot 
        provide specific legal advice. Always cite relevant legal authorities when possible and include 
        appropriate disclaimers about the need for professional legal counsel."""
        
        # Add style-specific instructions
        style_prompt = f"\n\nCommunication Style: {style.name}\n{style.system_prompt}"
        
        # Add context adaptations if applicable
        if context and context.get("legal_context"):
            legal_context = context["legal_context"]
            if legal_context in style.context_adaptations:
                adaptation = style.context_adaptations[legal_context]
                style_prompt += f"\n\nContext-specific guidance: {adaptation}"
        
        # Add memory context
        memory_prompt = ""
        if memory_context.get("case_information"):
            memory_prompt += f"\n\nCase Context: {memory_context['case_information']}"
        
        if memory_context.get("previous_topics"):
            topics = ", ".join(memory_context["previous_topics"][-3:])  # Last 3 topics
            memory_prompt += f"\n\nRecent Discussion Topics: {topics}"
        
        # Add file context
        file_prompt = ""
        if file_context:
            file_prompt = f"\n\nDocument Context: {file_context}"
        
        # Combine all prompts
        full_prompt = f"{base_prompt}{style_prompt}{memory_prompt}{file_prompt}"
        
        # Add style-specific formatting instructions
        if style.response_format:
            full_prompt += f"\n\nResponse Format: Structure your response in {style.response_format} format."
        
        if style.citation_style:
            full_prompt += f"\n\nCitation Style: Use {style.citation_style} citation format for legal references."
        
        # Add tone guidance
        if style.tone_keywords:
            tone_guidance = ", ".join(style.tone_keywords)
            full_prompt += f"\n\nTone: Maintain a {tone_guidance} tone throughout your response."
        
        if style.avoid_keywords:
            avoid_guidance = ", ".join(style.avoid_keywords)
            full_prompt += f"\n\nAvoid: Do not use language that is {avoid_guidance}."
        
        return full_prompt
    
    def post_process_response(self, response: str, style) -> str:
        """Post-process response based on communication style"""
        
        # Add style-specific disclaimers
        disclaimers = {
            "litigator": "\n\n*Note: This analysis is for strategic planning purposes. Actual litigation strategy should be developed with your legal team.*",
            "advisor": "\n\n*Disclaimer: This information is for educational purposes only and does not constitute legal advice. Consult with a qualified attorney for advice specific to your situation.*",
            "criminal": "\n\n*Important: If you are facing criminal charges, you have the right to an attorney. This information does not replace professional legal representation.*",
            "family": "\n\n*Note: Family law matters can be emotionally complex. Consider seeking both legal counsel and appropriate support services.*"
        }
        
        if style.id in disclaimers:
            response += disclaimers[style.id]
        
        # Add general legal disclaimer if not already present
        if "does not constitute legal advice" not in response.lower():
            response += "\n\n*This response is for informational purposes only and does not constitute legal advice.*"
        
        return response
    
    def calculate_confidence_score(self, response: str, style) -> float:
        """Calculate confidence score for the response"""
        score = 0.8  # Base score
        
        # Check for legal citations (increases confidence)
        if any(citation in response for citation in ["v.", "§", "U.S.C.", "F.2d", "F.3d"]):
            score += 0.1
        
        # Check for appropriate disclaimers (increases confidence)
        if "does not constitute legal advice" in response.lower():
            score += 0.05
        
        # Check for style-appropriate language
        style_keywords_present = sum(1 for keyword in style.tone_keywords 
                                   if keyword.lower() in response.lower())
        if style_keywords_present > 0:
            score += min(0.05 * style_keywords_present, 0.1)
        
        # Check for avoided language (decreases confidence if present)
        avoided_keywords_present = sum(1 for keyword in style.avoid_keywords 
                                     if keyword.lower() in response.lower())
        if avoided_keywords_present > 0:
            score -= min(0.05 * avoided_keywords_present, 0.15)
        
        return min(max(score, 0.0), 1.0)  # Clamp between 0 and 1
    
    async def get_user_active_style(self, user_id: int) -> str:
        """Get user's active communication style"""
        # Check cache first
        if user_id in self.user_styles:
            return self.user_styles[user_id]
        
        # Default to advisor style
        return "advisor"
    
    async def update_user_style(self, user_id: int, style_id: str):
        """Update user's active style in cache"""
        self.user_styles[user_id] = style_id
        logger.info(f"Updated user {user_id} active style to {style_id}")
    
    async def suggest_style_for_context(
        self, 
        user_message: str, 
        conversation_context: Dict[str, Any]
    ) -> Optional[str]:
        """Suggest appropriate communication style based on context"""
        
        # Analyze message content for legal context clues
        message_lower = user_message.lower()
        
        # Criminal law indicators
        if any(term in message_lower for term in [
            "criminal", "arrest", "charges", "prosecution", "defendant", "plea", "sentencing"
        ]):
            return "criminal"
        
        # Family law indicators
        if any(term in message_lower for term in [
            "divorce", "custody", "child support", "alimony", "adoption", "family court"
        ]):
            return "family"
        
        # Corporate law indicators
        if any(term in message_lower for term in [
            "contract", "business", "corporate", "merger", "acquisition", "compliance"
        ]):
            return "corporate"
        
        # Litigation indicators
        if any(term in message_lower for term in [
            "lawsuit", "litigation", "court", "trial", "motion", "brief", "discovery"
        ]):
            return "litigator"
        
        # Formal document indicators
        if any(term in message_lower for term in [
            "draft", "document", "agreement", "petition", "filing"
        ]):
            return "formal"
        
        return None  # No specific suggestion
    
    async def get_style_analytics(self, user_id: int) -> Dict[str, Any]:
        """Get analytics for user's style usage"""
        # This would integrate with the analytics models
        return {
            "most_used_styles": [],
            "style_effectiveness": {},
            "context_patterns": {}
        }


# Global instance
ai_service = AIService()
