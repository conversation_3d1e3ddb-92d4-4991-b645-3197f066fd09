"""
Advanced Context Engine for Legal AI Bot
Manages smart context selection, conversation continuity, and cross-thread memory
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from sentence_transformers import SentenceTransformer

from ..database import get_db
from ..models.conversation import Conversation, Message, ConversationSummary, ConversationContext
from ..models.user import User
from .memory_manager import memory_manager, MemoryContext

logger = logging.getLogger(__name__)


@dataclass
class ContextSelection:
    """Smart context selection result"""
    relevant_messages: List[Dict[str, Any]]
    conversation_summaries: List[Dict[str, Any]]
    document_chunks: List[Dict[str, Any]]
    key_topics: List[str]
    related_cases: List[str]
    context_confidence: float
    total_tokens: int
    selection_reasoning: str


@dataclass
class ConversationContinuation:
    """Conversation continuation context"""
    previous_conversation: List[Dict[str, Any]]
    relevant_history: List[Dict[str, Any]]
    conversation_summaries: List[Dict[str, Any]]
    key_topics: List[str]
    related_cases: List[str]
    context_confidence: float
    continuation_prompt: str
    suggested_title: str


class AdvancedContextEngine:
    """Advanced context engine with smart selection and conversation continuity"""
    
    def __init__(self):
        self.embedding_model = None
        self.max_context_tokens = 8000  # Reserve tokens for response
        self.min_relevance_score = 0.3
        self.topic_extraction_cache = {}
        
    async def initialize(self):
        """Initialize the context engine"""
        try:
            # Initialize embedding model
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            logger.info("Context engine initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize context engine: {str(e)}")
            raise
    
    async def get_smart_context(
        self,
        query: str,
        user_id: int,
        current_conversation_id: Optional[str] = None,
        practice_area: Optional[str] = None,
        case_id: Optional[str] = None,
        max_tokens: int = 8000
    ) -> ContextSelection:
        """Get smart context selection for a query"""
        try:
            # Get base memory context
            memory_context = await memory_manager.get_relevant_context(
                query=query,
                user_id=user_id,
                current_conversation_id=current_conversation_id,
                max_results=50,
                include_all_conversations=True
            )
            
            # Extract key topics from query
            query_topics = await self._extract_topics(query)
            
            # Get conversation summaries
            summaries = await self._get_relevant_summaries(
                user_id=user_id,
                topics=query_topics,
                practice_area=practice_area,
                case_id=case_id,
                limit=10
            )
            
            # Get document chunks if available
            document_chunks = await self._get_relevant_documents(
                query=query,
                user_id=user_id,
                case_id=case_id,
                limit=20
            )
            
            # Smart selection based on relevance and token limits
            selected_context = await self._smart_select_context(
                memory_context=memory_context,
                summaries=summaries,
                document_chunks=document_chunks,
                query_topics=query_topics,
                max_tokens=max_tokens
            )
            
            return selected_context
            
        except Exception as e:
            logger.error(f"Failed to get smart context: {str(e)}")
            # Return minimal context on error
            return ContextSelection(
                relevant_messages=[],
                conversation_summaries=[],
                document_chunks=[],
                key_topics=[],
                related_cases=[],
                context_confidence=0.0,
                total_tokens=0,
                selection_reasoning="Error occurred during context selection"
            )
    
    async def create_conversation_continuation(
        self,
        context_query: str,
        user_id: int,
        previous_conversation_id: Optional[str] = None,
        practice_area: Optional[str] = None
    ) -> ConversationContinuation:
        """Create a new conversation with full context from previous conversations"""
        try:
            async with get_db() as db:
                # Get previous conversation if specified
                previous_messages = []
                if previous_conversation_id:
                    previous_messages = await self._get_conversation_messages(
                        db, previous_conversation_id, limit=20
                    )
                
                # Get relevant context across all conversations
                context = await self.get_smart_context(
                    query=context_query,
                    user_id=user_id,
                    current_conversation_id=previous_conversation_id,
                    practice_area=practice_area,
                    max_tokens=6000  # Leave room for continuation prompt
                )
                
                # Generate continuation prompt
                continuation_prompt = await self._generate_continuation_prompt(
                    context_query=context_query,
                    previous_messages=previous_messages,
                    relevant_context=context,
                    practice_area=practice_area
                )
                
                # Suggest conversation title
                suggested_title = await self._suggest_conversation_title(
                    context_query, context.key_topics
                )
                
                return ConversationContinuation(
                    previous_conversation=previous_messages,
                    relevant_history=context.relevant_messages,
                    conversation_summaries=context.conversation_summaries,
                    key_topics=context.key_topics,
                    related_cases=context.related_cases,
                    context_confidence=context.context_confidence,
                    continuation_prompt=continuation_prompt,
                    suggested_title=suggested_title
                )
                
        except Exception as e:
            logger.error(f"Failed to create conversation continuation: {str(e)}")
            raise
    
    async def update_conversation_context(
        self,
        conversation_id: str,
        user_id: int,
        new_message: str,
        message_role: str = "user"
    ):
        """Update conversation context after new message"""
        try:
            async with get_db() as db:
                # Extract topics from new message
                topics = await self._extract_topics(new_message)
                
                # Find related conversations
                related_convs = await self._find_related_conversations(
                    db, user_id, topics, conversation_id
                )
                
                # Update conversation relationships
                await self._update_conversation_relationships(
                    db, conversation_id, related_convs, topics
                )
                
                # Update conversation summary if needed
                await self._maybe_update_conversation_summary(
                    db, conversation_id, user_id
                )
                
        except Exception as e:
            logger.error(f"Failed to update conversation context: {str(e)}")
    
    async def _smart_select_context(
        self,
        memory_context: MemoryContext,
        summaries: List[Dict[str, Any]],
        document_chunks: List[Dict[str, Any]],
        query_topics: List[str],
        max_tokens: int
    ) -> ContextSelection:
        """Smart selection of context based on relevance and token limits"""
        
        selected_messages = []
        selected_summaries = []
        selected_documents = []
        current_tokens = 0
        
        # Estimate tokens (rough approximation: 1 token ≈ 4 characters)
        def estimate_tokens(text: str) -> int:
            return len(text) // 4
        
        # Priority 1: High-relevance messages from memory context
        for msg in memory_context.relevant_messages[:20]:
            msg_tokens = estimate_tokens(msg['content'])
            if current_tokens + msg_tokens <= max_tokens * 0.5:  # Reserve 50% for messages
                selected_messages.append(msg)
                current_tokens += msg_tokens
            else:
                break
        
        # Priority 2: Relevant conversation summaries
        for summary in summaries[:5]:
            summary_tokens = estimate_tokens(summary['summary'])
            if current_tokens + summary_tokens <= max_tokens * 0.7:  # Up to 70% total
                selected_summaries.append(summary)
                current_tokens += summary_tokens
            else:
                break
        
        # Priority 3: Document chunks
        for doc in document_chunks[:10]:
            doc_tokens = estimate_tokens(doc['content'])
            if current_tokens + doc_tokens <= max_tokens * 0.9:  # Up to 90% total
                selected_documents.append(doc)
                current_tokens += doc_tokens
            else:
                break
        
        # Calculate confidence based on relevance scores and coverage
        confidence = self._calculate_context_confidence(
            selected_messages, selected_summaries, selected_documents, query_topics
        )
        
        # Generate selection reasoning
        reasoning = self._generate_selection_reasoning(
            len(selected_messages), len(selected_summaries), 
            len(selected_documents), current_tokens, max_tokens
        )
        
        return ContextSelection(
            relevant_messages=selected_messages,
            conversation_summaries=selected_summaries,
            document_chunks=selected_documents,
            key_topics=query_topics + memory_context.key_topics,
            related_cases=memory_context.related_cases,
            context_confidence=confidence,
            total_tokens=current_tokens,
            selection_reasoning=reasoning
        )
    
    async def _extract_topics(self, text: str) -> List[str]:
        """Extract key topics from text"""
        # Simple keyword extraction (can be enhanced with NLP)
        legal_keywords = [
            'contract', 'litigation', 'patent', 'trademark', 'copyright',
            'employment', 'corporate', 'real estate', 'family law',
            'criminal', 'civil', 'bankruptcy', 'tax', 'immigration',
            'intellectual property', 'merger', 'acquisition', 'compliance'
        ]
        
        text_lower = text.lower()
        found_topics = [keyword for keyword in legal_keywords if keyword in text_lower]
        
        # Add to cache for performance
        self.topic_extraction_cache[text[:100]] = found_topics
        
        return found_topics[:5]  # Limit to top 5 topics
    
    async def _get_relevant_summaries(
        self,
        user_id: int,
        topics: List[str],
        practice_area: Optional[str],
        case_id: Optional[str],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Get relevant conversation summaries"""
        try:
            async with get_db() as db:
                query = select(ConversationSummary).where(
                    ConversationSummary.user_id == user_id
                )
                
                if practice_area:
                    query = query.where(
                        ConversationSummary.practice_areas.contains([practice_area])
                    )
                
                if case_id:
                    query = query.where(
                        ConversationSummary.case_references.contains([case_id])
                    )
                
                query = query.order_by(desc(ConversationSummary.updated_at)).limit(limit)
                
                result = await db.execute(query)
                summaries = result.scalars().all()
                
                return [
                    {
                        'id': s.uuid,
                        'title': s.title,
                        'summary': s.summary,
                        'key_topics': s.topics or [],
                        'practice_areas': s.practice_areas or [],
                        'created_at': s.created_at.isoformat()
                    }
                    for s in summaries
                ]
                
        except Exception as e:
            logger.error(f"Failed to get relevant summaries: {str(e)}")
            return []
    
    async def _get_relevant_documents(
        self,
        query: str,
        user_id: int,
        case_id: Optional[str],
        limit: int
    ) -> List[Dict[str, Any]]:
        """Get relevant document chunks"""
        try:
            # Query document embeddings from ChromaDB
            if memory_manager.document_collection:
                results = memory_manager.document_collection.query(
                    query_texts=[query],
                    n_results=limit,
                    where={"user_id": user_id} if not case_id else {
                        "user_id": user_id,
                        "case_id": case_id
                    }
                )
                
                documents = []
                if results['documents'] and results['documents'][0]:
                    for i, doc in enumerate(results['documents'][0]):
                        metadata = results['metadatas'][0][i] if results['metadatas'] else {}
                        distance = results['distances'][0][i] if results['distances'] else 1.0
                        
                        documents.append({
                            'content': doc,
                            'metadata': metadata,
                            'relevance_score': 1.0 - distance,  # Convert distance to similarity
                            'source_file': metadata.get('source_file', 'Unknown'),
                            'chunk_index': metadata.get('chunk_index', 0)
                        })
                
                return documents
            
            return []
            
        except Exception as e:
            logger.error(f"Failed to get relevant documents: {str(e)}")
            return []
    
    async def _get_conversation_messages(
        self,
        db: AsyncSession,
        conversation_id: str,
        limit: int
    ) -> List[Dict[str, Any]]:
        """Get messages from a conversation"""
        try:
            # Get conversation
            conv_query = select(Conversation).where(Conversation.uuid == conversation_id)
            conv_result = await db.execute(conv_query)
            conversation = conv_result.scalar_one_or_none()
            
            if not conversation:
                return []
            
            # Get messages
            msg_query = select(Message).where(
                Message.conversation_id == conversation.id
            ).order_by(desc(Message.created_at)).limit(limit)
            
            msg_result = await db.execute(msg_query)
            messages = msg_result.scalars().all()
            
            return [
                {
                    'id': msg.uuid,
                    'role': msg.role,
                    'content': msg.content,
                    'timestamp': msg.created_at.isoformat(),
                    'metadata': msg.metadata or {}
                }
                for msg in reversed(messages)  # Reverse to get chronological order
            ]
            
        except Exception as e:
            logger.error(f"Failed to get conversation messages: {str(e)}")
            return []
    
    def _calculate_context_confidence(
        self,
        messages: List[Dict[str, Any]],
        summaries: List[Dict[str, Any]],
        documents: List[Dict[str, Any]],
        query_topics: List[str]
    ) -> float:
        """Calculate confidence score for context selection"""
        
        if not messages and not summaries and not documents:
            return 0.0
        
        # Base confidence from number of relevant items
        base_confidence = min(0.8, (len(messages) + len(summaries) + len(documents)) * 0.1)
        
        # Boost confidence if we have high-relevance items
        relevance_boost = 0.0
        for msg in messages:
            if msg.get('relevance_score', 0) > 0.7:
                relevance_boost += 0.05
        
        # Boost confidence if topics match
        topic_boost = len(query_topics) * 0.05 if query_topics else 0.0
        
        return min(1.0, base_confidence + relevance_boost + topic_boost)
    
    def _generate_selection_reasoning(
        self,
        num_messages: int,
        num_summaries: int,
        num_documents: int,
        used_tokens: int,
        max_tokens: int
    ) -> str:
        """Generate reasoning for context selection"""
        
        reasoning_parts = []
        
        if num_messages > 0:
            reasoning_parts.append(f"{num_messages} relevant messages")
        
        if num_summaries > 0:
            reasoning_parts.append(f"{num_summaries} conversation summaries")
        
        if num_documents > 0:
            reasoning_parts.append(f"{num_documents} document chunks")
        
        token_usage = f"Using {used_tokens}/{max_tokens} tokens ({used_tokens/max_tokens*100:.1f}%)"
        
        if reasoning_parts:
            return f"Selected {', '.join(reasoning_parts)}. {token_usage}"
        else:
            return f"No relevant context found. {token_usage}"
    
    async def _generate_continuation_prompt(
        self,
        context_query: str,
        previous_messages: List[Dict[str, Any]],
        relevant_context: ContextSelection,
        practice_area: Optional[str]
    ) -> str:
        """Generate continuation prompt for new conversation"""
        
        prompt_parts = [
            "You are continuing a legal conversation with full context from previous discussions.",
            f"Current query: {context_query}",
        ]
        
        if practice_area:
            prompt_parts.append(f"Practice area: {practice_area}")
        
        if relevant_context.key_topics:
            prompt_parts.append(f"Key topics: {', '.join(relevant_context.key_topics)}")
        
        if previous_messages:
            prompt_parts.append(f"Previous conversation had {len(previous_messages)} messages.")
        
        if relevant_context.relevant_messages:
            prompt_parts.append(f"Found {len(relevant_context.relevant_messages)} relevant messages from past conversations.")
        
        prompt_parts.append("Please provide a comprehensive response considering all available context.")
        
        return "\n".join(prompt_parts)
    
    async def _suggest_conversation_title(
        self,
        context_query: str,
        topics: List[str]
    ) -> str:
        """Suggest a title for the new conversation"""
        
        # Simple title generation based on query and topics
        if topics:
            main_topic = topics[0].title()
            return f"{main_topic} - {context_query[:50]}..."
        else:
            return f"Legal Consultation - {context_query[:50]}..."


# Global context engine instance
context_engine = AdvancedContextEngine()
