"""
DeepSeek API Client for Legal AI Bot
Handles all interactions with DeepSeek LLM with legal-specific optimizations
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, AsyncGenerator
from datetime import datetime
import httpx
from pydantic import BaseModel, Field

from ..config import settings


logger = logging.getLogger(__name__)


class ChatMessage(BaseModel):
    """Chat message model"""
    role: str = Field(..., description="Message role: system, user, or assistant")
    content: str = Field(..., description="Message content")
    timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow)


class LegalPromptTemplate(BaseModel):
    """Legal-specific prompt template"""
    name: str
    description: str
    system_prompt: str
    user_prompt_template: str
    parameters: Dict[str, Any] = Field(default_factory=dict)


class DeepSeekResponse(BaseModel):
    """DeepSeek API response model"""
    content: str
    usage: Dict[str, int]
    model: str
    finish_reason: str
    response_time: float
    confidence_score: Optional[float] = None


class DeepSeekClient:
    """
    DeepSeek API client with legal-specific features and anti-hallucination measures
    """
    
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.model = settings.DEEPSEEK_MODEL
        self.max_tokens = settings.DEEPSEEK_MAX_TOKENS
        self.temperature = settings.DEEPSEEK_TEMPERATURE
        
        # Legal-specific system prompts
        self.legal_system_prompts = self._load_legal_prompts()
        
        # HTTP client with timeout and retry configuration
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
    
    def _load_legal_prompts(self) -> Dict[str, LegalPromptTemplate]:
        """Load legal-specific prompt templates"""
        return {
            "legal_assistant": LegalPromptTemplate(
                name="Legal Assistant",
                description="General legal assistance with accuracy focus",
                system_prompt="""You are a professional legal AI assistant with expertise across all areas of law. 
                
CRITICAL REQUIREMENTS:
- Provide accurate, well-researched legal information
- Always cite relevant laws, cases, and regulations when applicable
- Use proper legal citation format (Bluebook or ALWD)
- Clearly distinguish between legal facts and legal opinions
- Never provide advice that could be considered practicing law without a license
- Always recommend consulting with a qualified attorney for specific legal matters
- If uncertain about any legal information, explicitly state your uncertainty
- Maintain client confidentiality and professional standards

AREAS OF EXPERTISE:
- Civil litigation and procedure
- Criminal law and procedure  
- Family law and domestic relations
- Corporate and business law
- Intellectual property law
- Employment and labor law
- Real estate and property law
- Tax law and regulations
- Immigration law
- Bankruptcy and insolvency
- Personal injury and tort law
- Environmental and regulatory law

RESPONSE FORMAT:
- Provide clear, structured responses
- Use legal terminology appropriately
- Include relevant citations and references
- Offer practical guidance within ethical bounds
- Suggest next steps or additional research when appropriate""",
                user_prompt_template="{user_query}",
                parameters={"temperature": 0.1, "max_tokens": 4000}
            ),
            
            "document_drafter": LegalPromptTemplate(
                name="Legal Document Drafter",
                description="Specialized in drafting legal documents",
                system_prompt="""You are a specialized legal document drafting assistant.

DOCUMENT DRAFTING REQUIREMENTS:
- Create professional, legally sound documents
- Use appropriate legal language and formatting
- Include all necessary clauses and provisions
- Ensure compliance with applicable laws and regulations
- Provide alternative clauses when appropriate
- Include explanatory comments for complex provisions
- Follow jurisdiction-specific requirements
- Maintain consistency in terminology throughout documents

DOCUMENT TYPES EXPERTISE:
- Contracts and agreements
- Legal briefs and memoranda
- Motions and pleadings
- Wills and estate planning documents
- Corporate documents and bylaws
- Employment agreements and policies
- Real estate documents
- Intellectual property agreements

FORMATTING STANDARDS:
- Use proper legal document structure
- Include appropriate headers and numbering
- Provide signature blocks and execution requirements
- Include necessary disclaimers and notices""",
                user_prompt_template="Draft a {document_type}: {requirements}",
                parameters={"temperature": 0.05, "max_tokens": 6000}
            ),
            
            "case_researcher": LegalPromptTemplate(
                name="Legal Case Researcher",
                description="Specialized in legal research and case analysis",
                system_prompt="""You are a legal research specialist focused on case law analysis and legal precedent research.

RESEARCH REQUIREMENTS:
- Conduct thorough legal research using authoritative sources
- Analyze case law and identify relevant precedents
- Provide comprehensive case summaries and holdings
- Identify key legal principles and their applications
- Compare and contrast different jurisdictional approaches
- Analyze statutory and regulatory frameworks
- Identify potential legal arguments and counterarguments

RESEARCH METHODOLOGY:
- Start with primary sources (cases, statutes, regulations)
- Use secondary sources for context and analysis
- Verify currency and validity of legal authorities
- Consider jurisdictional variations and conflicts
- Identify trends in legal interpretation
- Provide comprehensive citation information

OUTPUT FORMAT:
- Executive summary of findings
- Detailed analysis with supporting citations
- Identification of key cases and statutes
- Discussion of legal principles and applications
- Recommendations for further research""",
                user_prompt_template="Research: {research_query}. Jurisdiction: {jurisdiction}",
                parameters={"temperature": 0.1, "max_tokens": 8000}
            )
        }
    
    async def chat_completion(
        self,
        messages: List[ChatMessage],
        prompt_type: str = "legal_assistant",
        custom_system_prompt: Optional[str] = None,
        **kwargs
    ) -> DeepSeekResponse:
        """
        Generate chat completion with legal-specific optimizations
        """
        start_time = datetime.utcnow()
        
        try:
            # Prepare system prompt
            if custom_system_prompt:
                system_prompt = custom_system_prompt
            else:
                template = self.legal_system_prompts.get(prompt_type)
                if template:
                    system_prompt = template.system_prompt
                else:
                    system_prompt = self.legal_system_prompts["legal_assistant"].system_prompt
            
            # Prepare messages for API
            api_messages = [{"role": "system", "content": system_prompt}]
            
            for msg in messages:
                api_messages.append({
                    "role": msg.role,
                    "content": msg.content
                })
            
            # API request payload
            payload = {
                "model": self.model,
                "messages": api_messages,
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "top_p": kwargs.get("top_p", 0.95),
                "frequency_penalty": kwargs.get("frequency_penalty", 0.0),
                "presence_penalty": kwargs.get("presence_penalty", 0.0),
                "stream": False
            }
            
            # Make API request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                json=payload,
                headers=headers
            )
            
            response.raise_for_status()
            result = response.json()
            
            # Calculate response time
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            # Extract response data
            choice = result["choices"][0]
            content = choice["message"]["content"]
            usage = result.get("usage", {})
            finish_reason = choice.get("finish_reason", "unknown")
            
            # Validate response for legal accuracy
            confidence_score = await self._validate_legal_response(content)
            
            return DeepSeekResponse(
                content=content,
                usage=usage,
                model=result.get("model", self.model),
                finish_reason=finish_reason,
                response_time=response_time,
                confidence_score=confidence_score
            )
            
        except httpx.HTTPStatusError as e:
            logger.error(f"DeepSeek API error: {e.response.status_code} - {e.response.text}")
            raise Exception(f"DeepSeek API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"DeepSeek client error: {str(e)}")
            raise
    
    async def stream_completion(
        self,
        messages: List[ChatMessage],
        prompt_type: str = "legal_assistant",
        **kwargs
    ) -> AsyncGenerator[str, None]:
        """
        Stream chat completion for real-time responses
        """
        try:
            # Prepare system prompt
            template = self.legal_system_prompts.get(prompt_type)
            system_prompt = template.system_prompt if template else self.legal_system_prompts["legal_assistant"].system_prompt
            
            # Prepare messages
            api_messages = [{"role": "system", "content": system_prompt}]
            for msg in messages:
                api_messages.append({"role": msg.role, "content": msg.content})
            
            # API request payload
            payload = {
                "model": self.model,
                "messages": api_messages,
                "max_tokens": kwargs.get("max_tokens", self.max_tokens),
                "temperature": kwargs.get("temperature", self.temperature),
                "stream": True
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/chat/completions",
                json=payload,
                headers=headers
            ) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        data = line[6:]  # Remove "data: " prefix
                        if data == "[DONE]":
                            break
                        
                        try:
                            chunk = json.loads(data)
                            if "choices" in chunk and len(chunk["choices"]) > 0:
                                delta = chunk["choices"][0].get("delta", {})
                                if "content" in delta:
                                    yield delta["content"]
                        except json.JSONDecodeError:
                            continue
                            
        except Exception as e:
            logger.error(f"DeepSeek streaming error: {str(e)}")
            raise
    
    async def _validate_legal_response(self, content: str) -> float:
        """
        Validate legal response for accuracy and completeness
        Returns confidence score between 0.0 and 1.0
        """
        try:
            # Basic validation checks
            confidence_factors = []
            
            # Check for legal citations
            if any(pattern in content.lower() for pattern in ["v.", "§", "u.s.c.", "f.2d", "f.3d"]):
                confidence_factors.append(0.2)
            
            # Check for legal disclaimers
            if any(phrase in content.lower() for phrase in ["consult", "attorney", "legal advice", "qualified"]):
                confidence_factors.append(0.2)
            
            # Check for structured legal reasoning
            if any(word in content.lower() for word in ["therefore", "however", "pursuant", "whereas"]):
                confidence_factors.append(0.1)
            
            # Check for appropriate legal terminology
            legal_terms = ["jurisdiction", "precedent", "statute", "regulation", "case law", "holding"]
            if any(term in content.lower() for term in legal_terms):
                confidence_factors.append(0.2)
            
            # Check response length (detailed responses generally more reliable)
            if len(content) > 500:
                confidence_factors.append(0.1)
            
            # Base confidence
            base_confidence = 0.2
            
            return min(1.0, base_confidence + sum(confidence_factors))
            
        except Exception as e:
            logger.warning(f"Response validation error: {str(e)}")
            return 0.5  # Default moderate confidence
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()


# Global client instance
deepseek_client = DeepSeekClient()
