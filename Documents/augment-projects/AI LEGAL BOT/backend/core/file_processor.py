"""
Advanced File Processing System for Legal AI Bot
Handles large file uploads, chunked processing, and content extraction
"""

import asyncio
import logging
import hashlib
import mimetypes
import tempfile
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional, AsyncGenerator, BinaryIO
from dataclasses import dataclass
from datetime import datetime
import uuid

import aiofiles
import aiofiles.os
from fastapi import UploadFile
import PyPDF2
import docx
import pytesseract
from PIL import Image
import fitz  # PyMuPDF
from sentence_transformers import SentenceTransformer

from ..config import settings
from ..models.document import Document
from .memory_manager import memory_manager

logger = logging.getLogger(__name__)


@dataclass
class FileProcessingResult:
    """Result of file processing operation"""
    file_id: str
    filename: str
    file_size: int
    content_type: str
    extracted_text: str
    page_count: Optional[int]
    processing_time: float
    metadata: Dict[str, Any]
    chunks: List[Dict[str, Any]]
    embeddings_created: bool
    error: Optional[str] = None


@dataclass
class UploadProgress:
    """Upload progress tracking"""
    file_id: str
    filename: str
    total_size: int
    uploaded_size: int
    progress_percentage: float
    status: str  # 'uploading', 'processing', 'completed', 'error'
    error_message: Optional[str] = None


class LargeFileProcessor:
    """Advanced file processor with chunked uploads and content extraction"""
    
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.temp_dir = Path(settings.TEMP_DIR)
        self.chunk_size = 8 * 1024 * 1024  # 8MB chunks
        self.max_file_size = 10 * 1024 * 1024 * 1024  # 10GB max
        self.supported_types = {
            'application/pdf': self._process_pdf,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._process_docx,
            'application/msword': self._process_doc,
            'text/plain': self._process_text,
            'text/rtf': self._process_rtf,
            'image/jpeg': self._process_image,
            'image/png': self._process_image,
            'image/tiff': self._process_image
        }
        self.active_uploads = {}
        self.embedding_model = None
        
    async def initialize(self):
        """Initialize the file processor"""
        try:
            # Create directories
            self.upload_dir.mkdir(parents=True, exist_ok=True)
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            
            # Initialize embedding model for content chunking
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            logger.info("File processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize file processor: {str(e)}")
            raise
    
    async def start_chunked_upload(
        self, 
        filename: str, 
        file_size: int, 
        content_type: str,
        user_id: int,
        case_id: Optional[str] = None
    ) -> str:
        """Start a chunked file upload"""
        try:
            # Validate file size
            if file_size > self.max_file_size:
                raise ValueError(f"File size {file_size} exceeds maximum {self.max_file_size}")
            
            # Generate unique file ID
            file_id = str(uuid.uuid4())
            
            # Create temporary file path
            temp_path = self.temp_dir / f"{file_id}_{filename}"
            
            # Initialize upload tracking
            self.active_uploads[file_id] = UploadProgress(
                file_id=file_id,
                filename=filename,
                total_size=file_size,
                uploaded_size=0,
                progress_percentage=0.0,
                status='uploading'
            )
            
            # Create empty file
            async with aiofiles.open(temp_path, 'wb') as f:
                pass
            
            logger.info(f"Started chunked upload for {filename} (ID: {file_id})")
            return file_id
            
        except Exception as e:
            logger.error(f"Failed to start chunked upload: {str(e)}")
            raise
    
    async def upload_chunk(
        self, 
        file_id: str, 
        chunk_data: bytes, 
        chunk_offset: int
    ) -> UploadProgress:
        """Upload a file chunk"""
        try:
            if file_id not in self.active_uploads:
                raise ValueError(f"Upload {file_id} not found")
            
            progress = self.active_uploads[file_id]
            temp_path = self.temp_dir / f"{file_id}_{progress.filename}"
            
            # Write chunk to file
            async with aiofiles.open(temp_path, 'r+b') as f:
                await f.seek(chunk_offset)
                await f.write(chunk_data)
            
            # Update progress
            progress.uploaded_size = chunk_offset + len(chunk_data)
            progress.progress_percentage = (progress.uploaded_size / progress.total_size) * 100
            
            logger.debug(f"Uploaded chunk for {file_id}: {progress.progress_percentage:.1f}%")
            return progress
            
        except Exception as e:
            logger.error(f"Failed to upload chunk: {str(e)}")
            if file_id in self.active_uploads:
                self.active_uploads[file_id].status = 'error'
                self.active_uploads[file_id].error_message = str(e)
            raise
    
    async def complete_upload(
        self, 
        file_id: str, 
        user_id: int,
        case_id: Optional[str] = None,
        process_immediately: bool = True
    ) -> FileProcessingResult:
        """Complete file upload and optionally process immediately"""
        try:
            if file_id not in self.active_uploads:
                raise ValueError(f"Upload {file_id} not found")
            
            progress = self.active_uploads[file_id]
            temp_path = self.temp_dir / f"{file_id}_{progress.filename}"
            
            # Verify file integrity
            actual_size = await aiofiles.os.path.getsize(temp_path)
            if actual_size != progress.total_size:
                raise ValueError(f"File size mismatch: expected {progress.total_size}, got {actual_size}")
            
            # Move to permanent location
            final_path = self.upload_dir / f"{file_id}_{progress.filename}"
            await aiofiles.os.rename(temp_path, final_path)
            
            # Update progress
            progress.status = 'processing' if process_immediately else 'completed'
            
            # Process file if requested
            if process_immediately:
                result = await self.process_file(
                    final_path, 
                    progress.filename,
                    user_id,
                    case_id
                )
                progress.status = 'completed'
                
                # Clean up from active uploads
                del self.active_uploads[file_id]
                
                return result
            else:
                # Clean up from active uploads
                del self.active_uploads[file_id]
                
                return FileProcessingResult(
                    file_id=file_id,
                    filename=progress.filename,
                    file_size=progress.total_size,
                    content_type=mimetypes.guess_type(progress.filename)[0] or 'application/octet-stream',
                    extracted_text="",
                    page_count=None,
                    processing_time=0.0,
                    metadata={},
                    chunks=[],
                    embeddings_created=False
                )
                
        except Exception as e:
            logger.error(f"Failed to complete upload: {str(e)}")
            if file_id in self.active_uploads:
                self.active_uploads[file_id].status = 'error'
                self.active_uploads[file_id].error_message = str(e)
            raise
    
    async def process_file(
        self, 
        file_path: Path, 
        filename: str,
        user_id: int,
        case_id: Optional[str] = None
    ) -> FileProcessingResult:
        """Process uploaded file and extract content"""
        start_time = datetime.now()
        
        try:
            # Determine content type
            content_type = mimetypes.guess_type(filename)[0] or 'application/octet-stream'
            file_size = await aiofiles.os.path.getsize(file_path)
            
            # Get file hash for deduplication
            file_hash = await self._calculate_file_hash(file_path)
            
            # Extract content based on file type
            if content_type in self.supported_types:
                extracted_text, metadata = await self.supported_types[content_type](file_path)
            else:
                extracted_text = ""
                metadata = {"unsupported_type": True}
            
            # Create content chunks for better processing
            chunks = await self._create_content_chunks(extracted_text, filename)
            
            # Create embeddings for chunks
            embeddings_created = False
            if chunks and self.embedding_model:
                await self._create_embeddings(chunks, user_id, case_id)
                embeddings_created = True
            
            processing_time = (datetime.now() - start_time).total_seconds()
            
            result = FileProcessingResult(
                file_id=str(uuid.uuid4()),
                filename=filename,
                file_size=file_size,
                content_type=content_type,
                extracted_text=extracted_text,
                page_count=metadata.get('page_count'),
                processing_time=processing_time,
                metadata={
                    **metadata,
                    'file_hash': file_hash,
                    'processed_at': datetime.now().isoformat()
                },
                chunks=chunks,
                embeddings_created=embeddings_created
            )
            
            logger.info(f"Processed file {filename} in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Failed to process file {filename}: {str(e)}")
            return FileProcessingResult(
                file_id=str(uuid.uuid4()),
                filename=filename,
                file_size=0,
                content_type=content_type,
                extracted_text="",
                page_count=None,
                processing_time=(datetime.now() - start_time).total_seconds(),
                metadata={},
                chunks=[],
                embeddings_created=False,
                error=str(e)
            )
    
    async def _process_pdf(self, file_path: Path) -> tuple[str, Dict[str, Any]]:
        """Process PDF file and extract text"""
        try:
            text_content = []
            metadata = {}
            
            # Use PyMuPDF for better text extraction
            doc = fitz.open(file_path)
            metadata['page_count'] = len(doc)
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                
                if text.strip():
                    text_content.append(f"--- Page {page_num + 1} ---\n{text}")
                else:
                    # Try OCR if no text found
                    pix = page.get_pixmap()
                    img_data = pix.tobytes("png")
                    
                    # Save temporary image for OCR
                    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as temp_img:
                        temp_img.write(img_data)
                        temp_img_path = temp_img.name
                    
                    try:
                        ocr_text = pytesseract.image_to_string(Image.open(temp_img_path))
                        if ocr_text.strip():
                            text_content.append(f"--- Page {page_num + 1} (OCR) ---\n{ocr_text}")
                    except Exception as ocr_error:
                        logger.warning(f"OCR failed for page {page_num + 1}: {str(ocr_error)}")
                    finally:
                        Path(temp_img_path).unlink(missing_ok=True)
            
            doc.close()
            
            full_text = "\n\n".join(text_content)
            metadata['extraction_method'] = 'pymupdf_with_ocr'
            metadata['character_count'] = len(full_text)
            
            return full_text, metadata
            
        except Exception as e:
            logger.error(f"PDF processing failed: {str(e)}")
            return "", {"error": str(e)}
    
    async def _process_docx(self, file_path: Path) -> tuple[str, Dict[str, Any]]:
        """Process DOCX file and extract text"""
        try:
            doc = docx.Document(file_path)
            
            text_content = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text)
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(" | ".join(row_text))
            
            full_text = "\n".join(text_content)
            
            metadata = {
                'paragraph_count': len(doc.paragraphs),
                'table_count': len(doc.tables),
                'character_count': len(full_text),
                'extraction_method': 'python_docx'
            }
            
            return full_text, metadata
            
        except Exception as e:
            logger.error(f"DOCX processing failed: {str(e)}")
            return "", {"error": str(e)}
    
    async def _process_text(self, file_path: Path) -> tuple[str, Dict[str, Any]]:
        """Process plain text file"""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = await f.read()
            
            metadata = {
                'character_count': len(content),
                'line_count': content.count('\n'),
                'extraction_method': 'direct_read'
            }
            
            return content, metadata
            
        except Exception as e:
            logger.error(f"Text processing failed: {str(e)}")
            return "", {"error": str(e)}
    
    async def _process_image(self, file_path: Path) -> tuple[str, Dict[str, Any]]:
        """Process image file with OCR"""
        try:
            # Use OCR to extract text from image
            text = pytesseract.image_to_string(Image.open(file_path))
            
            metadata = {
                'extraction_method': 'tesseract_ocr',
                'character_count': len(text)
            }
            
            return text, metadata
            
        except Exception as e:
            logger.error(f"Image processing failed: {str(e)}")
            return "", {"error": str(e)}
    
    async def _create_content_chunks(self, text: str, filename: str) -> List[Dict[str, Any]]:
        """Create semantic chunks from extracted text"""
        if not text.strip():
            return []
        
        # Split into paragraphs first
        paragraphs = [p.strip() for p in text.split('\n\n') if p.strip()]
        
        chunks = []
        current_chunk = ""
        chunk_size = 1000  # Characters per chunk
        
        for paragraph in paragraphs:
            if len(current_chunk) + len(paragraph) > chunk_size and current_chunk:
                chunks.append({
                    "content": current_chunk.strip(),
                    "chunk_index": len(chunks),
                    "source_file": filename,
                    "chunk_type": "text"
                })
                current_chunk = paragraph
            else:
                current_chunk += "\n\n" + paragraph if current_chunk else paragraph
        
        # Add final chunk
        if current_chunk.strip():
            chunks.append({
                "content": current_chunk.strip(),
                "chunk_index": len(chunks),
                "source_file": filename,
                "chunk_type": "text"
            })
        
        return chunks
    
    async def _create_embeddings(self, chunks: List[Dict[str, Any]], user_id: int, case_id: Optional[str]):
        """Create embeddings for content chunks"""
        try:
            for chunk in chunks:
                embedding = self.embedding_model.encode(chunk["content"]).tolist()
                
                # Add to vector database
                chunk_id = f"{user_id}_{chunk['source_file']}_{chunk['chunk_index']}"
                
                memory_manager.document_collection.add(
                    embeddings=[embedding],
                    documents=[chunk["content"]],
                    metadatas=[{
                        "user_id": user_id,
                        "case_id": case_id or "",
                        "source_file": chunk["source_file"],
                        "chunk_index": chunk["chunk_index"],
                        "chunk_type": chunk["chunk_type"]
                    }],
                    ids=[chunk_id]
                )
                
        except Exception as e:
            logger.error(f"Failed to create embeddings: {str(e)}")
    
    async def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file"""
        hash_sha256 = hashlib.sha256()
        async with aiofiles.open(file_path, 'rb') as f:
            while chunk := await f.read(8192):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    
    def get_upload_progress(self, file_id: str) -> Optional[UploadProgress]:
        """Get upload progress for a file"""
        return self.active_uploads.get(file_id)
    
    def get_all_active_uploads(self) -> List[UploadProgress]:
        """Get all active uploads"""
        return list(self.active_uploads.values())


# Global file processor instance
file_processor = LargeFileProcessor()
