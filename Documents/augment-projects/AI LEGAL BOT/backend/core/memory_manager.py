"""
Advanced Memory Management System for Legal AI Bot
Handles cross-conversation context, vector search, and global memory
"""

import asyncio
import logging
import json
import hashlib
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np

import chromadb
from chromadb.config import Settings
from sentence_transformers import SentenceTransformer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc

from ..database import get_db
from ..models.conversation import Conversation, Message
from ..models.user import User
from ..config import settings

logger = logging.getLogger(__name__)


@dataclass
class MemoryContext:
    """Memory context for conversation enhancement"""
    relevant_messages: List[Dict[str, Any]]
    conversation_summaries: List[Dict[str, Any]]
    key_topics: List[str]
    related_cases: List[str]
    confidence_score: float
    total_context_length: int


@dataclass
class ConversationSummary:
    """Conversation summary for memory storage"""
    conversation_id: str
    user_id: int
    title: str
    summary: str
    key_topics: List[str]
    practice_areas: List[str]
    important_points: List[str]
    created_at: datetime
    updated_at: datetime
    message_count: int


class AdvancedMemoryManager:
    """Advanced memory management with vector search and cross-conversation context"""
    
    def __init__(self):
        self.embedding_model = None
        self.chroma_client = None
        self.conversation_collection = None
        self.document_collection = None
        self.memory_cache = {}
        self.max_context_length = settings.MAX_CONTEXT_LENGTH
        self.max_memory_cache_size = 1000
        
    async def initialize(self):
        """Initialize the memory manager"""
        try:
            # Initialize embedding model
            logger.info("Loading embedding model...")
            self.embedding_model = SentenceTransformer('all-MiniLM-L6-v2')
            
            # Initialize ChromaDB
            logger.info("Initializing vector database...")
            self.chroma_client = chromadb.PersistentClient(
                path=str(settings.VECTOR_DB_PATH),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Create collections
            self.conversation_collection = self.chroma_client.get_or_create_collection(
                name="conversations",
                metadata={"hnsw:space": "cosine"}
            )
            
            self.document_collection = self.chroma_client.get_or_create_collection(
                name="documents",
                metadata={"hnsw:space": "cosine"}
            )
            
            logger.info("Memory manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize memory manager: {str(e)}")
            raise
    
    async def add_message_to_memory(
        self, 
        message: Message, 
        conversation: Conversation,
        user: User
    ):
        """Add a message to the memory system with embeddings"""
        try:
            # Create embedding for the message
            embedding = self.embedding_model.encode(message.content).tolist()
            
            # Create unique ID for the message
            message_id = f"{conversation.uuid}_{message.uuid}"
            
            # Prepare metadata
            metadata = {
                "conversation_id": conversation.uuid,
                "user_id": user.id,
                "message_role": message.role,
                "timestamp": message.created_at.isoformat(),
                "practice_area": conversation.practice_area or "general",
                "case_id": conversation.case_uuid or "",
                "message_type": message.metadata.get("type", "text") if message.metadata else "text"
            }
            
            # Add to vector database
            self.conversation_collection.add(
                embeddings=[embedding],
                documents=[message.content],
                metadatas=[metadata],
                ids=[message_id]
            )
            
            # Update conversation summary
            await self._update_conversation_summary(conversation, user)
            
            logger.debug(f"Added message {message.uuid} to memory")
            
        except Exception as e:
            logger.error(f"Failed to add message to memory: {str(e)}")
    
    async def get_relevant_context(
        self, 
        query: str, 
        user_id: int,
        current_conversation_id: Optional[str] = None,
        max_results: int = 20,
        include_all_conversations: bool = True
    ) -> MemoryContext:
        """Get relevant context from all conversations"""
        try:
            # Create embedding for the query
            query_embedding = self.embedding_model.encode(query).tolist()
            
            # Search in vector database
            search_results = self.conversation_collection.query(
                query_embeddings=[query_embedding],
                n_results=max_results * 2,  # Get more to filter
                where={"user_id": user_id} if not include_all_conversations else None
            )
            
            relevant_messages = []
            conversation_summaries = []
            key_topics = set()
            related_cases = set()
            
            if search_results['documents'] and search_results['documents'][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    search_results['documents'][0],
                    search_results['metadatas'][0],
                    search_results['distances'][0]
                )):
                    # Skip current conversation messages if specified
                    if (current_conversation_id and 
                        metadata['conversation_id'] == current_conversation_id):
                        continue
                    
                    # Only include high-relevance results
                    if distance < 0.7:  # Cosine similarity threshold
                        relevant_messages.append({
                            "content": doc,
                            "role": metadata['message_role'],
                            "timestamp": metadata['timestamp'],
                            "conversation_id": metadata['conversation_id'],
                            "practice_area": metadata['practice_area'],
                            "case_id": metadata['case_id'],
                            "relevance_score": 1 - distance
                        })
                        
                        key_topics.add(metadata['practice_area'])
                        if metadata['case_id']:
                            related_cases.add(metadata['case_id'])
            
            # Get conversation summaries for related conversations
            conversation_ids = list(set([msg['conversation_id'] for msg in relevant_messages]))
            for conv_id in conversation_ids[:10]:  # Limit to top 10 conversations
                summary = await self._get_conversation_summary(conv_id, user_id)
                if summary:
                    conversation_summaries.append(summary)
            
            # Calculate total context length
            total_length = sum(len(msg['content']) for msg in relevant_messages)
            
            # Trim context if too long
            if total_length > self.max_context_length:
                relevant_messages = self._trim_context(relevant_messages, self.max_context_length)
                total_length = sum(len(msg['content']) for msg in relevant_messages)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(relevant_messages)
            
            return MemoryContext(
                relevant_messages=relevant_messages[:max_results],
                conversation_summaries=conversation_summaries,
                key_topics=list(key_topics),
                related_cases=list(related_cases),
                confidence_score=confidence_score,
                total_context_length=total_length
            )
            
        except Exception as e:
            logger.error(f"Failed to get relevant context: {str(e)}")
            return MemoryContext([], [], [], [], 0.0, 0)
    
    async def get_conversation_history(
        self, 
        conversation_id: str, 
        limit: int = 50,
        include_context: bool = True
    ) -> List[Dict[str, Any]]:
        """Get conversation history with optional cross-conversation context"""
        try:
            async with get_db() as db:
                # Get conversation messages
                query = select(Message).where(
                    Message.conversation_id == conversation_id
                ).order_by(Message.created_at).limit(limit)
                
                result = await db.execute(query)
                messages = result.scalars().all()
                
                history = []
                for message in messages:
                    msg_dict = {
                        "id": message.uuid,
                        "role": message.role,
                        "content": message.content,
                        "timestamp": message.created_at,
                        "metadata": message.metadata or {}
                    }
                    
                    # Add relevant context if requested
                    if include_context and message.role == "user":
                        context = await self.get_relevant_context(
                            message.content,
                            message.user_id,
                            current_conversation_id=conversation_id,
                            max_results=5
                        )
                        msg_dict["relevant_context"] = {
                            "messages": context.relevant_messages[:3],
                            "topics": context.key_topics,
                            "cases": context.related_cases
                        }
                    
                    history.append(msg_dict)
                
                return history
                
        except Exception as e:
            logger.error(f"Failed to get conversation history: {str(e)}")
            return []
    
    async def create_conversation_continuation(
        self, 
        user_id: int, 
        context_query: str,
        previous_conversation_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create a new conversation with full context from previous conversations"""
        try:
            # Get comprehensive context
            context = await self.get_relevant_context(
                context_query,
                user_id,
                max_results=30,
                include_all_conversations=True
            )
            
            # If previous conversation specified, get its full context
            previous_context = []
            if previous_conversation_id:
                previous_context = await self.get_conversation_history(
                    previous_conversation_id,
                    limit=100,
                    include_context=False
                )
            
            # Create continuation context
            continuation_context = {
                "previous_conversation": previous_context[-20:] if previous_context else [],
                "relevant_history": context.relevant_messages,
                "conversation_summaries": context.conversation_summaries,
                "key_topics": context.key_topics,
                "related_cases": context.related_cases,
                "context_confidence": context.confidence_score,
                "continuation_prompt": self._generate_continuation_prompt(context, previous_context)
            }
            
            return continuation_context
            
        except Exception as e:
            logger.error(f"Failed to create conversation continuation: {str(e)}")
            return {}
    
    async def search_all_conversations(
        self, 
        query: str, 
        user_id: int,
        filters: Optional[Dict[str, Any]] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """Search across all conversations with advanced filtering"""
        try:
            query_embedding = self.embedding_model.encode(query).tolist()
            
            # Build where clause
            where_clause = {"user_id": user_id}
            if filters:
                if filters.get("practice_area"):
                    where_clause["practice_area"] = filters["practice_area"]
                if filters.get("case_id"):
                    where_clause["case_id"] = filters["case_id"]
                if filters.get("message_type"):
                    where_clause["message_type"] = filters["message_type"]
            
            # Search
            results = self.conversation_collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                where=where_clause
            )
            
            search_results = []
            if results['documents'] and results['documents'][0]:
                for doc, metadata, distance in zip(
                    results['documents'][0],
                    results['metadatas'][0],
                    results['distances'][0]
                ):
                    search_results.append({
                        "content": doc,
                        "metadata": metadata,
                        "relevance_score": 1 - distance,
                        "conversation_id": metadata['conversation_id']
                    })
            
            return search_results
            
        except Exception as e:
            logger.error(f"Failed to search conversations: {str(e)}")
            return []
    
    def _trim_context(self, messages: List[Dict[str, Any]], max_length: int) -> List[Dict[str, Any]]:
        """Trim context to fit within length limits"""
        # Sort by relevance score
        messages.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        trimmed = []
        current_length = 0
        
        for message in messages:
            msg_length = len(message['content'])
            if current_length + msg_length <= max_length:
                trimmed.append(message)
                current_length += msg_length
            else:
                break
        
        return trimmed
    
    def _calculate_confidence_score(self, messages: List[Dict[str, Any]]) -> float:
        """Calculate confidence score for context relevance"""
        if not messages:
            return 0.0
        
        scores = [msg.get('relevance_score', 0) for msg in messages]
        return sum(scores) / len(scores)
    
    def _generate_continuation_prompt(
        self, 
        context: MemoryContext, 
        previous_context: List[Dict[str, Any]]
    ) -> str:
        """Generate a prompt for conversation continuation"""
        prompt_parts = []
        
        if previous_context:
            prompt_parts.append("Previous conversation context:")
            recent_messages = previous_context[-5:]
            for msg in recent_messages:
                prompt_parts.append(f"{msg['role']}: {msg['content'][:200]}...")
        
        if context.relevant_messages:
            prompt_parts.append("\nRelevant historical context:")
            for msg in context.relevant_messages[:3]:
                prompt_parts.append(f"- {msg['content'][:150]}...")
        
        if context.key_topics:
            prompt_parts.append(f"\nKey topics: {', '.join(context.key_topics)}")
        
        if context.related_cases:
            prompt_parts.append(f"Related cases: {', '.join(context.related_cases)}")
        
        return "\n".join(prompt_parts)
    
    async def _update_conversation_summary(self, conversation: Conversation, user: User):
        """Update conversation summary in background"""
        # This would be implemented to periodically update conversation summaries
        pass
    
    async def _get_conversation_summary(self, conversation_id: str, user_id: int) -> Optional[Dict[str, Any]]:
        """Get conversation summary from cache or database"""
        # This would retrieve stored conversation summaries
        return None


# Global memory manager instance
memory_manager = AdvancedMemoryManager()
