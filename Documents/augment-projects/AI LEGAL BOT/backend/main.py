"""
Legal AI Bot - FastAPI Application
Main application entry point with comprehensive legal AI capabilities
"""

import logging
import sys
from contextlib import asynccontextmanager
from datetime import datetime
from typing import Dict, Any

from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
import uvicorn

from .config import settings
from .database import engine, Base, get_db
from .core.deepseek_client import deepseek_client
from .utils.logger import setup_logging

# Import API routers
from .api import chat, documents, cases, files, auth

# Setup logging
setup_logging()
logger = logging.getLogger(__name__)

# Security
security = HTTPBearer()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan management"""
    # Startup
    logger.info("Starting Legal AI Bot application...")
    
    try:
        # Create database tables
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Database tables created successfully")
        
        # Initialize services
        logger.info("Services initialized successfully")
        
        yield
        
    except Exception as e:
        logger.error(f"Startup error: {str(e)}")
        sys.exit(1)
    
    finally:
        # Shutdown
        logger.info("Shutting down Legal AI Bot application...")
        await deepseek_client.close()
        await engine.dispose()
        logger.info("Application shutdown complete")


# Create FastAPI application
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="""
    Professional Legal AI Assistant with comprehensive legal capabilities.
    
    ## Features
    
    * **Legal Document Drafting** - Professional-grade document generation
    * **Case Research** - Comprehensive legal research and analysis  
    * **Litigation Support** - Case building and evidence analysis
    * **Citation Accuracy** - Verified legal citations with format validation
    * **Memory Retention** - Full conversation and case context preservation
    * **File Processing** - Support for PDF, DOCX, and other legal document formats
    * **Security** - Client confidentiality and data protection compliance
    
    ## Legal Disclaimer
    
    This AI assistant provides legal information for educational and research purposes only.
    It does not constitute legal advice and should not be relied upon as a substitute for
    consultation with a qualified attorney. Always consult with a licensed attorney for
    specific legal matters.
    """,
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    lifespan=lifespan
)

# Middleware configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", settings.HOST]
)


# Global exception handler
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Global exception handler for unhandled errors"""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    
    if settings.DEBUG:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "detail": str(exc),
                "timestamp": datetime.utcnow().isoformat()
            }
        )
    else:
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "message": "An unexpected error occurred. Please try again later.",
                "timestamp": datetime.utcnow().isoformat()
            }
        )


# Health check endpoint
@app.get("/health", tags=["System"])
async def health_check():
    """System health check endpoint"""
    try:
        # Check database connection
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")
        
        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": settings.APP_VERSION,
            "services": {
                "database": "connected",
                "deepseek": "available"
            }
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service unhealthy"
        )


# System information endpoint
@app.get("/info", tags=["System"])
async def system_info():
    """Get system information"""
    return {
        "name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "description": "Professional Legal AI Assistant",
        "features": [
            "Legal Document Drafting",
            "Case Research & Analysis", 
            "Litigation Support",
            "Citation Verification",
            "Memory Retention",
            "File Processing",
            "Security & Compliance"
        ],
        "supported_formats": [
            "PDF", "DOCX", "DOC", "TXT", "RTF", "XLS", "XLSX"
        ],
        "legal_areas": [
            "Civil Litigation", "Criminal Law", "Family Law",
            "Corporate Law", "Intellectual Property", "Employment Law",
            "Real Estate", "Tax Law", "Immigration", "Bankruptcy"
        ]
    }


# Include API routers
app.include_router(
    auth.router,
    prefix="/api/v1/auth",
    tags=["Authentication"]
)

app.include_router(
    chat.router,
    prefix="/api/v1/chat",
    tags=["Chat & Conversation"]
)

app.include_router(
    documents.router,
    prefix="/api/v1/documents",
    tags=["Document Management"]
)

app.include_router(
    cases.router,
    prefix="/api/v1/cases",
    tags=["Case Management"]
)

app.include_router(
    files.router,
    prefix="/api/v1/files",
    tags=["File Processing"]
)


# Root endpoint
@app.get("/", tags=["System"])
async def root():
    """Root endpoint with application information"""
    return {
        "message": "Legal AI Bot - Professional Legal Assistant",
        "version": settings.APP_VERSION,
        "documentation": "/docs",
        "health": "/health",
        "info": "/info"
    }


# Legal disclaimer endpoint
@app.get("/disclaimer", tags=["Legal"])
async def legal_disclaimer():
    """Legal disclaimer and terms of use"""
    return {
        "disclaimer": """
        LEGAL DISCLAIMER
        
        This Legal AI Bot provides legal information for educational and research purposes only.
        The information provided by this system:
        
        1. Does NOT constitute legal advice
        2. Should NOT be relied upon as a substitute for consultation with a qualified attorney
        3. May not be current, complete, or accurate for your specific situation
        4. Does not create an attorney-client relationship
        
        IMPORTANT NOTICES:
        
        - Always consult with a licensed attorney for specific legal matters
        - Legal requirements vary by jurisdiction and change over time
        - This system may contain errors or omissions
        - Professional legal judgment is required for legal decision-making
        
        By using this system, you acknowledge that you understand these limitations
        and agree to use the information provided at your own risk.
        """,
        "last_updated": datetime.utcnow().isoformat(),
        "contact": "For legal advice, please consult with a qualified attorney in your jurisdiction."
    }


if __name__ == "__main__":
    # Run the application
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.RELOAD,
        log_level=settings.LOG_LEVEL.lower(),
        access_log=True
    )
