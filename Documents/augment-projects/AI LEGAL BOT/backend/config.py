"""
Configuration management for Legal AI Bot
Handles environment variables and application settings
"""

import os
from typing import Optional, List
from pydantic_settings import BaseSettings
from pydantic import Field, validator


class Settings(BaseSettings):
    """Application settings with environment variable support"""
    
    # Application
    APP_NAME: str = "Legal AI Bot"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = Field(default=False, env="DEBUG")
    
    # Server
    HOST: str = Field(default="localhost", env="HOST")
    PORT: int = Field(default=8000, env="PORT")
    RELOAD: bool = Field(default=False, env="RELOAD")
    
    # Database
    DATABASE_URL: str = Field(env="DATABASE_URL")
    DATABASE_ECHO: bool = Field(default=False, env="DATABASE_ECHO")
    
    # DeepSeek API
    DEEPSEEK_API_KEY: str = Field(env="DEEPSEEK_API_KEY")
    DEEPSEEK_BASE_URL: str = Field(
        default="https://api.deepseek.com/v1", 
        env="DEEPSEEK_BASE_URL"
    )
    DEEPSEEK_MODEL: str = Field(
        default="deepseek-chat", 
        env="DEEPSEEK_MODEL"
    )
    DEEPSEEK_MAX_TOKENS: int = Field(default=4000, env="DEEPSEEK_MAX_TOKENS")
    DEEPSEEK_TEMPERATURE: float = Field(default=0.1, env="DEEPSEEK_TEMPERATURE")
    
    # Security
    SECRET_KEY: str = Field(env="SECRET_KEY")
    ALGORITHM: str = Field(default="HS256", env="ALGORITHM")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, 
        env="ACCESS_TOKEN_EXPIRE_MINUTES"
    )
    
    # File Upload
    MAX_FILE_SIZE: int = Field(default=100 * 1024 * 1024, env="MAX_FILE_SIZE")  # 100MB
    ALLOWED_FILE_TYPES: List[str] = Field(
        default=[
            "application/pdf",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/msword",
            "text/plain",
            "text/rtf",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        ],
        env="ALLOWED_FILE_TYPES"
    )
    UPLOAD_DIR: str = Field(default="uploads", env="UPLOAD_DIR")
    
    # Legal Features
    ENABLE_CITATION_VALIDATION: bool = Field(
        default=True, 
        env="ENABLE_CITATION_VALIDATION"
    )
    LEGAL_DATABASES: List[str] = Field(
        default=["westlaw", "lexis", "google_scholar"],
        env="LEGAL_DATABASES"
    )
    
    # Memory and Context
    MAX_CONVERSATION_HISTORY: int = Field(
        default=10000, 
        env="MAX_CONVERSATION_HISTORY"
    )
    CONTEXT_WINDOW_SIZE: int = Field(
        default=8000, 
        env="CONTEXT_WINDOW_SIZE"
    )
    
    # Vector Database
    VECTOR_DB_PATH: str = Field(default="./vector_db", env="VECTOR_DB_PATH")
    EMBEDDING_MODEL: str = Field(
        default="sentence-transformers/all-MiniLM-L6-v2",
        env="EMBEDDING_MODEL"
    )
    
    # Redis (for caching and background tasks)
    REDIS_URL: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="legal_ai_bot.log", env="LOG_FILE")
    
    # CORS
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        env="CORS_ORIGINS"
    )
    
    # Legal Compliance
    AUDIT_LOG_ENABLED: bool = Field(default=True, env="AUDIT_LOG_ENABLED")
    DATA_RETENTION_DAYS: int = Field(default=2555, env="DATA_RETENTION_DAYS")  # 7 years
    ENCRYPTION_ENABLED: bool = Field(default=True, env="ENCRYPTION_ENABLED")
    
    @validator("DATABASE_URL")
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    @validator("DEEPSEEK_API_KEY")
    def validate_deepseek_api_key(cls, v):
        if not v:
            raise ValueError("DEEPSEEK_API_KEY is required")
        return v
    
    @validator("SECRET_KEY")
    def validate_secret_key(cls, v):
        if not v:
            raise ValueError("SECRET_KEY is required")
        if len(v) < 32:
            raise ValueError("SECRET_KEY must be at least 32 characters long")
        return v
    
    @validator("DEEPSEEK_TEMPERATURE")
    def validate_temperature(cls, v):
        if not 0.0 <= v <= 2.0:
            raise ValueError("DEEPSEEK_TEMPERATURE must be between 0.0 and 2.0")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings


# Legal-specific configuration
class LegalConfig:
    """Legal-specific configuration and constants"""
    
    # Citation formats
    CITATION_FORMATS = {
        "bluebook": {
            "case": "{case_name}, {volume} {reporter} {page} ({court} {year})",
            "statute": "{title} U.S.C. § {section} ({year})",
            "regulation": "{title} C.F.R. § {section} ({year})"
        },
        "alwd": {
            "case": "{case_name}, {volume} {reporter} {page} ({court} {year})",
            "statute": "{title} U.S.C. § {section} ({year})",
            "regulation": "{title} C.F.R. § {section} ({year})"
        }
    }
    
    # Legal document types
    DOCUMENT_TYPES = [
        "contract", "brief", "motion", "pleading", "memorandum",
        "opinion", "order", "judgment", "settlement", "discovery",
        "deposition", "affidavit", "will", "trust", "lease",
        "employment_agreement", "nda", "licensing_agreement"
    ]
    
    # Practice areas
    PRACTICE_AREAS = [
        "civil_litigation", "criminal_law", "family_law", "corporate_law",
        "intellectual_property", "employment_law", "real_estate",
        "tax_law", "immigration", "bankruptcy", "personal_injury",
        "environmental_law", "healthcare_law", "securities_law"
    ]
    
    # Court systems
    COURT_SYSTEMS = {
        "federal": ["Supreme Court", "Circuit Court", "District Court"],
        "state": ["Supreme Court", "Appellate Court", "Trial Court"],
        "specialty": ["Tax Court", "Bankruptcy Court", "Immigration Court"]
    }


legal_config = LegalConfig()
