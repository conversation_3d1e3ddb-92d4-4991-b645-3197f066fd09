"""
API endpoints for communication styles management
"""

from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
import json

from ..database import get_db
from ..models.user import User
from ..models.communication_style import (
    UserCommunicationStyle, 
    ConversationStyleHistory, 
    StyleTemplate,
    StyleAnalytics
)
from ..core.communication_styles import legal_styles, StyleCategory
from ..core.auth import get_current_user
from ..core.ai_service import ai_service

router = APIRouter(prefix="/api/communication-styles", tags=["Communication Styles"])


# Pydantic models
class StyleResponse(BaseModel):
    id: str
    name: str
    category: str
    description: str
    use_cases: List[str]
    tone_keywords: List[str]
    example_phrases: List[str]


class StyleCombinationResponse(BaseModel):
    id: str
    name: str
    description: str
    primary_style: str
    secondary_style: str


class UserStylePreferences(BaseModel):
    active_style_id: str
    preferred_styles: List[str] = []
    context_style_mappings: Dict[str, str] = {}
    auto_switch_enabled: bool = False
    auto_switch_rules: List[Dict[str, Any]] = []


class StyleChangeRequest(BaseModel):
    style_id: str
    conversation_id: Optional[int] = None
    context: Optional[str] = None
    reason: Optional[str] = None


class CustomStyleTemplate(BaseModel):
    template_id: str
    name: str
    description: str
    base_style_id: str
    custom_system_prompt: Optional[str] = None
    temperature_override: Optional[float] = None
    additional_tone_keywords: List[str] = []
    custom_example_phrases: List[str] = []
    tags: List[str] = []
    practice_areas: List[str] = []
    is_public: bool = False


@router.get("/styles", response_model=List[StyleResponse])
async def get_all_styles():
    """Get all available communication styles"""
    styles = legal_styles.get_all_styles()
    return [
        StyleResponse(
            id=style.id,
            name=style.name,
            category=style.category.value,
            description=style.description,
            use_cases=style.use_cases,
            tone_keywords=style.tone_keywords,
            example_phrases=style.example_phrases
        )
        for style in styles
    ]


@router.get("/styles/categories")
async def get_style_categories():
    """Get all style categories with their styles"""
    categories = {}
    for category in StyleCategory:
        styles = legal_styles.get_styles_by_category(category)
        categories[category.value] = [
            {
                "id": style.id,
                "name": style.name,
                "description": style.description,
                "use_cases": style.use_cases
            }
            for style in styles
        ]
    return categories


@router.get("/styles/{style_id}")
async def get_style_details(style_id: str):
    """Get detailed information about a specific style"""
    style = legal_styles.get_style(style_id)
    if not style:
        raise HTTPException(status_code=404, detail="Style not found")
    
    return {
        "id": style.id,
        "name": style.name,
        "category": style.category.value,
        "description": style.description,
        "use_cases": style.use_cases,
        "system_prompt": style.system_prompt,
        "temperature": style.temperature,
        "max_tokens": style.max_tokens,
        "response_format": style.response_format,
        "citation_style": style.citation_style,
        "tone_keywords": style.tone_keywords,
        "avoid_keywords": style.avoid_keywords,
        "example_phrases": style.example_phrases,
        "context_adaptations": style.context_adaptations
    }


@router.get("/combinations", response_model=List[StyleCombinationResponse])
async def get_style_combinations():
    """Get available style combinations"""
    combinations = legal_styles.style_combinations
    return [
        StyleCombinationResponse(
            id=combo_id,
            name=combo_data["name"],
            description=combo_data["description"],
            primary_style=combo_data["primary"],
            secondary_style=combo_data["secondary"]
        )
        for combo_id, combo_data in combinations.items()
    ]


@router.get("/user/preferences", response_model=UserStylePreferences)
async def get_user_style_preferences(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get current user's style preferences"""
    result = await db.execute(
        select(UserCommunicationStyle).where(
            UserCommunicationStyle.user_id == current_user.id
        )
    )
    user_style = result.scalar_one_or_none()
    
    if not user_style:
        # Create default preferences
        user_style = UserCommunicationStyle(
            user_id=current_user.id,
            active_style_id="advisor"
        )
        db.add(user_style)
        await db.commit()
        await db.refresh(user_style)
    
    return UserStylePreferences(
        active_style_id=user_style.active_style_id,
        preferred_styles=user_style.get_preferred_styles(),
        context_style_mappings=user_style.context_style_mappings or {},
        auto_switch_enabled=user_style.auto_switch_enabled,
        auto_switch_rules=user_style.auto_switch_rules or []
    )


@router.put("/user/preferences")
async def update_user_style_preferences(
    preferences: UserStylePreferences,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Update user's style preferences"""
    # Validate style exists
    if not legal_styles.get_style(preferences.active_style_id):
        raise HTTPException(status_code=400, detail="Invalid style ID")
    
    result = await db.execute(
        select(UserCommunicationStyle).where(
            UserCommunicationStyle.user_id == current_user.id
        )
    )
    user_style = result.scalar_one_or_none()
    
    if not user_style:
        user_style = UserCommunicationStyle(user_id=current_user.id)
        db.add(user_style)
    
    user_style.active_style_id = preferences.active_style_id
    user_style.preferred_styles = preferences.preferred_styles
    user_style.context_style_mappings = preferences.context_style_mappings
    user_style.auto_switch_enabled = preferences.auto_switch_enabled
    user_style.auto_switch_rules = preferences.auto_switch_rules
    
    await db.commit()
    return {"message": "Preferences updated successfully"}


@router.post("/user/change-style")
async def change_active_style(
    request: StyleChangeRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Change the user's active communication style"""
    # Validate style exists
    style = legal_styles.get_style(request.style_id)
    if not style:
        raise HTTPException(status_code=400, detail="Invalid style ID")
    
    # Update user's active style
    result = await db.execute(
        select(UserCommunicationStyle).where(
            UserCommunicationStyle.user_id == current_user.id
        )
    )
    user_style = result.scalar_one_or_none()
    
    if not user_style:
        user_style = UserCommunicationStyle(user_id=current_user.id)
        db.add(user_style)
    
    old_style_id = user_style.active_style_id
    user_style.active_style_id = request.style_id
    user_style.increment_style_usage(request.style_id)
    
    # Record style change in conversation history if conversation_id provided
    if request.conversation_id:
        style_history = ConversationStyleHistory(
            conversation_id=request.conversation_id,
            user_id=current_user.id,
            style_id=request.style_id,
            trigger_context=request.context or "user_request",
            trigger_message=request.reason,
            style_config={
                "system_prompt": style.system_prompt,
                "temperature": style.temperature,
                "max_tokens": style.max_tokens,
                "response_format": style.response_format
            }
        )
        db.add(style_history)
    
    await db.commit()
    
    # Update AI service with new style (background task)
    background_tasks.add_task(
        ai_service.update_user_style,
        current_user.id,
        request.style_id
    )
    
    return {
        "message": "Style changed successfully",
        "old_style": old_style_id,
        "new_style": request.style_id,
        "style_name": style.name
    }


@router.get("/user/history")
async def get_user_style_history(
    limit: int = 50,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's style change history"""
    result = await db.execute(
        select(ConversationStyleHistory)
        .where(ConversationStyleHistory.user_id == current_user.id)
        .order_by(ConversationStyleHistory.started_at.desc())
        .limit(limit)
    )
    
    history = result.scalars().all()
    
    return [
        {
            "id": record.id,
            "style_id": record.style_id,
            "style_combination": record.style_combination,
            "trigger_context": record.trigger_context,
            "trigger_message": record.trigger_message,
            "started_at": record.started_at,
            "ended_at": record.ended_at,
            "conversation_id": record.conversation_id
        }
        for record in history
    ]


@router.get("/user/analytics")
async def get_user_style_analytics(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """Get user's style usage analytics"""
    result = await db.execute(
        select(UserCommunicationStyle).where(
            UserCommunicationStyle.user_id == current_user.id
        )
    )
    user_style = result.scalar_one_or_none()
    
    if not user_style:
        return {"most_used_styles": [], "usage_stats": {}}
    
    most_used = user_style.get_most_used_styles()
    usage_stats = user_style.style_usage_count or {}
    
    # Get style names for most used styles
    most_used_with_names = []
    for style_id in most_used:
        style = legal_styles.get_style(style_id)
        if style:
            most_used_with_names.append({
                "style_id": style_id,
                "style_name": style.name,
                "usage_count": usage_stats.get(style_id, 0)
            })
    
    return {
        "most_used_styles": most_used_with_names,
        "usage_stats": usage_stats,
        "total_style_changes": sum(usage_stats.values()),
        "preferred_styles": user_style.get_preferred_styles()
    }
