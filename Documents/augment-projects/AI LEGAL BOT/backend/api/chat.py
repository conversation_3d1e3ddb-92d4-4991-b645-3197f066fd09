"""
Chat API endpoints for Legal AI Bot
Handles conversation management and AI interactions
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_db
from ..core.deepseek_client import deepseek_client, ChatMessage
from ..models.user import User
from ..models.conversation import Conversation, Message
from ..core.memory_manager import memory_manager
from ..utils.security import get_current_user
from ..utils.validators import validate_legal_query

logger = logging.getLogger(__name__)

router = APIRouter()


class ChatRequest(BaseModel):
    """Chat request model"""
    message: str = Field(..., min_length=1, max_length=10000, description="User message")
    conversation_id: Optional[str] = Field(None, description="Conversation ID for context")
    case_id: Optional[str] = Field(None, description="Associated case ID")
    prompt_type: str = Field(default="legal_assistant", description="Type of legal assistance")
    context: Optional[Dict[str, Any]] = Field(default=None, description="Additional context")
    stream: bool = Field(default=False, description="Enable streaming response")


class ChatResponse(BaseModel):
    """Chat response model"""
    message: str = Field(..., description="AI response message")
    conversation_id: str = Field(..., description="Conversation ID")
    message_id: str = Field(..., description="Message ID")
    confidence_score: Optional[float] = Field(None, description="Response confidence score")
    citations: List[str] = Field(default=[], description="Legal citations referenced")
    suggestions: List[str] = Field(default=[], description="Follow-up suggestions")
    metadata: Dict[str, Any] = Field(default={}, description="Response metadata")


class ConversationSummary(BaseModel):
    """Conversation summary model"""
    id: str
    title: str
    created_at: datetime
    updated_at: datetime
    message_count: int
    case_id: Optional[str] = None
    practice_area: Optional[str] = None


@router.post("/send", response_model=ChatResponse)
async def send_message(
    request: ChatRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Send a message to the legal AI assistant
    """
    try:
        # Validate legal query
        validation_result = await validate_legal_query(request.message)
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid legal query: {validation_result.error_message}"
            )
        
        # Get or create conversation
        conversation = await get_or_create_conversation(
            db, current_user.id, request.conversation_id, request.case_id
        )
        
        # Get conversation history for context
        conversation_history = await memory_manager.get_conversation_history(
            conversation.uuid, limit=20
        )
        
        # Prepare messages for AI
        messages = []
        for hist_msg in conversation_history:
            messages.append(ChatMessage(
                role=hist_msg.role,
                content=hist_msg.content,
                timestamp=hist_msg.created_at
            ))
        
        # Add current user message
        messages.append(ChatMessage(
            role="user",
            content=request.message,
            timestamp=datetime.utcnow()
        ))
        
        # Save user message to database
        user_message = await save_message(
            db, conversation.id, "user", request.message, current_user.id
        )
        
        # Get AI response
        ai_response = await deepseek_client.chat_completion(
            messages=messages,
            prompt_type=request.prompt_type,
            context=request.context
        )
        
        # Save AI response to database
        ai_message = await save_message(
            db, conversation.id, "assistant", ai_response.content, None
        )
        
        # Extract citations and suggestions
        citations = extract_citations(ai_response.content)
        suggestions = generate_suggestions(ai_response.content, request.prompt_type)
        
        # Update conversation metadata
        background_tasks.add_task(
            update_conversation_metadata,
            db, conversation.id, request.message, ai_response.content
        )
        
        # Log interaction for audit
        background_tasks.add_task(
            log_chat_interaction,
            current_user.id, conversation.uuid, request.message, ai_response.content
        )
        
        return ChatResponse(
            message=ai_response.content,
            conversation_id=conversation.uuid,
            message_id=ai_message.uuid,
            confidence_score=ai_response.confidence_score,
            citations=citations,
            suggestions=suggestions,
            metadata={
                "response_time": ai_response.response_time,
                "model": ai_response.model,
                "prompt_type": request.prompt_type,
                "usage": ai_response.usage
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Chat error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while processing your message"
        )


@router.post("/stream")
async def stream_message(
    request: ChatRequest,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Stream a message response from the legal AI assistant
    """
    if not request.stream:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Streaming must be enabled for this endpoint"
        )
    
    try:
        # Validate legal query
        validation_result = await validate_legal_query(request.message)
        if not validation_result.is_valid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid legal query: {validation_result.error_message}"
            )
        
        # Get or create conversation
        conversation = await get_or_create_conversation(
            db, current_user.id, request.conversation_id, request.case_id
        )
        
        # Get conversation history
        conversation_history = await memory_manager.get_conversation_history(
            conversation.uuid, limit=20
        )
        
        # Prepare messages
        messages = []
        for hist_msg in conversation_history:
            messages.append(ChatMessage(
                role=hist_msg.role,
                content=hist_msg.content,
                timestamp=hist_msg.created_at
            ))
        
        messages.append(ChatMessage(
            role="user",
            content=request.message,
            timestamp=datetime.utcnow()
        ))
        
        # Save user message
        await save_message(db, conversation.id, "user", request.message, current_user.id)
        
        async def generate_stream():
            """Generate streaming response"""
            full_response = ""
            
            try:
                async for chunk in deepseek_client.stream_completion(
                    messages=messages,
                    prompt_type=request.prompt_type
                ):
                    full_response += chunk
                    yield f"data: {chunk}\n\n"
                
                # Save complete AI response
                await save_message(db, conversation.id, "assistant", full_response, None)
                
                yield "data: [DONE]\n\n"
                
            except Exception as e:
                logger.error(f"Streaming error: {str(e)}")
                yield f"data: ERROR: {str(e)}\n\n"
        
        return StreamingResponse(
            generate_stream(),
            media_type="text/plain",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Stream chat error: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while streaming your message"
        )


@router.get("/conversations", response_model=List[ConversationSummary])
async def get_conversations(
    limit: int = 50,
    offset: int = 0,
    case_id: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user's conversation history
    """
    try:
        conversations = await get_user_conversations(
            db, current_user.id, limit, offset, case_id
        )
        
        return [
            ConversationSummary(
                id=conv.uuid,
                title=conv.title or "Untitled Conversation",
                created_at=conv.created_at,
                updated_at=conv.updated_at,
                message_count=len(conv.messages),
                case_id=conv.case_uuid,
                practice_area=conv.practice_area
            )
            for conv in conversations
        ]
        
    except Exception as e:
        logger.error(f"Get conversations error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversations"
        )


@router.get("/conversations/{conversation_id}/messages")
async def get_conversation_messages(
    conversation_id: str,
    limit: int = 100,
    offset: int = 0,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get messages from a specific conversation
    """
    try:
        messages = await get_conversation_messages_from_db(
            db, conversation_id, current_user.id, limit, offset
        )
        
        return {
            "conversation_id": conversation_id,
            "messages": [
                {
                    "id": msg.uuid,
                    "role": msg.role,
                    "content": msg.content,
                    "created_at": msg.created_at.isoformat(),
                    "metadata": msg.metadata
                }
                for msg in messages
            ]
        }
        
    except Exception as e:
        logger.error(f"Get conversation messages error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve conversation messages"
        )


@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a conversation and all its messages
    """
    try:
        success = await delete_user_conversation(db, conversation_id, current_user.id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        return {"message": "Conversation deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete conversation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete conversation"
        )


# Helper functions (to be implemented in separate modules)
async def get_or_create_conversation(db, user_id, conversation_id, case_id):
    """Get existing conversation or create new one"""
    # Implementation would go here
    pass

async def save_message(db, conversation_id, role, content, user_id):
    """Save message to database"""
    # Implementation would go here
    pass

def extract_citations(content: str) -> List[str]:
    """Extract legal citations from AI response"""
    # Implementation would go here
    return []

def generate_suggestions(content: str, prompt_type: str) -> List[str]:
    """Generate follow-up suggestions based on response"""
    # Implementation would go here
    return []

async def update_conversation_metadata(db, conversation_id, user_message, ai_response):
    """Update conversation metadata in background"""
    # Implementation would go here
    pass

async def log_chat_interaction(user_id, conversation_id, user_message, ai_response):
    """Log chat interaction for audit purposes"""
    # Implementation would go here
    pass

async def get_user_conversations(db, user_id, limit, offset, case_id):
    """Get user conversations from database"""
    # Implementation would go here
    return []

async def get_conversation_messages_from_db(db, conversation_id, user_id, limit, offset):
    """Get conversation messages from database"""
    # Implementation would go here
    return []

async def delete_user_conversation(db, conversation_id, user_id):
    """Delete user conversation from database"""
    # Implementation would go here
    return True
