"""
File Upload API endpoints for Legal AI Bot
Handles large file uploads with chunked processing
"""

import logging
import asyncio
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import J<PERSON>NResponse
from pydantic import BaseModel, Field
from sqlalchemy.ext.asyncio import AsyncSession

from ..database import get_db
from ..core.file_processor import file_processor, FileProcessingResult, UploadProgress
from ..models.user import User
from ..models.document import Document
from ..utils.security import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter()


class ChunkedUploadStart(BaseModel):
    """Start chunked upload request"""
    filename: str = Field(..., min_length=1, max_length=255)
    file_size: int = Field(..., gt=0, le=10*1024*1024*1024)  # Max 10GB
    content_type: str = Field(..., min_length=1)
    case_id: Optional[str] = Field(None)
    process_immediately: bool = Field(default=True)


class ChunkedUploadResponse(BaseModel):
    """Chunked upload response"""
    file_id: str
    upload_url: str
    chunk_size: int
    max_chunks: int


class UploadProgressResponse(BaseModel):
    """Upload progress response"""
    file_id: str
    filename: str
    total_size: int
    uploaded_size: int
    progress_percentage: float
    status: str
    error_message: Optional[str] = None


class FileProcessingResponse(BaseModel):
    """File processing response"""
    file_id: str
    filename: str
    file_size: int
    content_type: str
    extracted_text: str
    page_count: Optional[int]
    processing_time: float
    metadata: Dict[str, Any]
    chunks_created: int
    embeddings_created: bool
    error: Optional[str] = None


@router.post("/upload/start", response_model=ChunkedUploadResponse)
async def start_chunked_upload(
    request: ChunkedUploadStart,
    current_user: User = Depends(get_current_user)
):
    """
    Start a chunked file upload for large files
    """
    try:
        # Validate file type
        allowed_types = [
            'application/pdf',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'text/plain',
            'text/rtf',
            'image/jpeg',
            'image/png',
            'image/tiff'
        ]
        
        if request.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"File type {request.content_type} not supported"
            )
        
        # Start chunked upload
        file_id = await file_processor.start_chunked_upload(
            filename=request.filename,
            file_size=request.file_size,
            content_type=request.content_type,
            user_id=current_user.id,
            case_id=request.case_id
        )
        
        # Calculate chunk information
        chunk_size = file_processor.chunk_size
        max_chunks = (request.file_size + chunk_size - 1) // chunk_size
        
        return ChunkedUploadResponse(
            file_id=file_id,
            upload_url=f"/api/files/upload/chunk/{file_id}",
            chunk_size=chunk_size,
            max_chunks=max_chunks
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start chunked upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start file upload"
        )


@router.post("/upload/chunk/{file_id}")
async def upload_chunk(
    file_id: str,
    chunk_offset: int = Form(...),
    chunk_data: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """
    Upload a file chunk
    """
    try:
        # Read chunk data
        data = await chunk_data.read()
        
        # Upload chunk
        progress = await file_processor.upload_chunk(
            file_id=file_id,
            chunk_data=data,
            chunk_offset=chunk_offset
        )
        
        return UploadProgressResponse(
            file_id=progress.file_id,
            filename=progress.filename,
            total_size=progress.total_size,
            uploaded_size=progress.uploaded_size,
            progress_percentage=progress.progress_percentage,
            status=progress.status,
            error_message=progress.error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to upload chunk: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload chunk"
        )


@router.post("/upload/complete/{file_id}", response_model=FileProcessingResponse)
async def complete_upload(
    file_id: str,
    case_id: Optional[str] = Form(None),
    process_immediately: bool = Form(True),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Complete file upload and optionally process immediately
    """
    try:
        # Complete upload
        result = await file_processor.complete_upload(
            file_id=file_id,
            user_id=current_user.id,
            case_id=case_id,
            process_immediately=process_immediately
        )
        
        # Save document record to database
        background_tasks.add_task(
            save_document_record,
            db, result, current_user.id, case_id
        )
        
        return FileProcessingResponse(
            file_id=result.file_id,
            filename=result.filename,
            file_size=result.file_size,
            content_type=result.content_type,
            extracted_text=result.extracted_text[:1000] + "..." if len(result.extracted_text) > 1000 else result.extracted_text,
            page_count=result.page_count,
            processing_time=result.processing_time,
            metadata=result.metadata,
            chunks_created=len(result.chunks),
            embeddings_created=result.embeddings_created,
            error=result.error
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to complete upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete upload"
        )


@router.get("/upload/progress/{file_id}", response_model=UploadProgressResponse)
async def get_upload_progress(
    file_id: str,
    current_user: User = Depends(get_current_user)
):
    """
    Get upload progress for a file
    """
    try:
        progress = file_processor.get_upload_progress(file_id)
        
        if not progress:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Upload not found"
            )
        
        return UploadProgressResponse(
            file_id=progress.file_id,
            filename=progress.filename,
            total_size=progress.total_size,
            uploaded_size=progress.uploaded_size,
            progress_percentage=progress.progress_percentage,
            status=progress.status,
            error_message=progress.error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get upload progress: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get upload progress"
        )


@router.get("/upload/active", response_model=List[UploadProgressResponse])
async def get_active_uploads(
    current_user: User = Depends(get_current_user)
):
    """
    Get all active uploads for the current user
    """
    try:
        active_uploads = file_processor.get_all_active_uploads()
        
        return [
            UploadProgressResponse(
                file_id=upload.file_id,
                filename=upload.filename,
                total_size=upload.total_size,
                uploaded_size=upload.uploaded_size,
                progress_percentage=upload.progress_percentage,
                status=upload.status,
                error_message=upload.error_message
            )
            for upload in active_uploads
        ]
        
    except Exception as e:
        logger.error(f"Failed to get active uploads: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get active uploads"
        )


@router.post("/upload/simple", response_model=FileProcessingResponse)
async def simple_upload(
    file: UploadFile = File(...),
    case_id: Optional[str] = Form(None),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Simple file upload for smaller files (< 100MB)
    """
    try:
        # Check file size
        max_simple_size = 100 * 1024 * 1024  # 100MB
        
        # Read file content
        content = await file.read()
        file_size = len(content)
        
        if file_size > max_simple_size:
            raise HTTPException(
                status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
                detail=f"File too large for simple upload. Use chunked upload for files > {max_simple_size} bytes"
            )
        
        # Save temporary file
        import tempfile
        import aiofiles
        from pathlib import Path
        
        with tempfile.NamedTemporaryFile(delete=False, suffix=f"_{file.filename}") as temp_file:
            temp_path = Path(temp_file.name)
            
        async with aiofiles.open(temp_path, 'wb') as f:
            await f.write(content)
        
        try:
            # Process file
            result = await file_processor.process_file(
                file_path=temp_path,
                filename=file.filename,
                user_id=current_user.id,
                case_id=case_id
            )
            
            # Save document record
            background_tasks.add_task(
                save_document_record,
                db, result, current_user.id, case_id
            )
            
            return FileProcessingResponse(
                file_id=result.file_id,
                filename=result.filename,
                file_size=result.file_size,
                content_type=result.content_type,
                extracted_text=result.extracted_text[:1000] + "..." if len(result.extracted_text) > 1000 else result.extracted_text,
                page_count=result.page_count,
                processing_time=result.processing_time,
                metadata=result.metadata,
                chunks_created=len(result.chunks),
                embeddings_created=result.embeddings_created,
                error=result.error
            )
            
        finally:
            # Clean up temporary file
            temp_path.unlink(missing_ok=True)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Simple upload failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


@router.get("/documents", response_model=List[Dict[str, Any]])
async def get_user_documents(
    limit: int = 50,
    offset: int = 0,
    case_id: Optional[str] = None,
    content_type: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get user's uploaded documents
    """
    try:
        # This would query the documents table
        # Implementation depends on Document model
        documents = []  # Placeholder
        
        return documents
        
    except Exception as e:
        logger.error(f"Failed to get documents: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve documents"
        )


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a document and its associated data
    """
    try:
        # This would delete the document and clean up files
        # Implementation depends on Document model
        
        return {"message": "Document deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete document: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete document"
        )


@router.post("/documents/{document_id}/reprocess")
async def reprocess_document(
    document_id: str,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Reprocess a document (re-extract text and create embeddings)
    """
    try:
        # This would reprocess an existing document
        background_tasks.add_task(reprocess_document_task, document_id, current_user.id)
        
        return {"message": "Document reprocessing started"}
        
    except Exception as e:
        logger.error(f"Failed to start document reprocessing: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start document reprocessing"
        )


# Background tasks
async def save_document_record(
    db: AsyncSession, 
    result: FileProcessingResult, 
    user_id: int, 
    case_id: Optional[str]
):
    """Save document record to database"""
    try:
        # This would save the document record
        # Implementation depends on Document model
        logger.info(f"Document record saved for {result.filename}")
        
    except Exception as e:
        logger.error(f"Failed to save document record: {str(e)}")


async def reprocess_document_task(document_id: str, user_id: int):
    """Reprocess document in background"""
    try:
        # This would reprocess the document
        logger.info(f"Reprocessing document {document_id}")
        
    except Exception as e:
        logger.error(f"Failed to reprocess document: {str(e)}")
