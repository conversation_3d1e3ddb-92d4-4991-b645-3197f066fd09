"""
Admin API endpoints for deployment management and user administration
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from fastapi.security import HTTPBearer
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging

from ..database import get_db
from ..models.user import User
from ..models.tenant import Tenant, TenantSubscription, DeploymentInstance, DeploymentLog, UsageMetrics
from ..core.security import get_current_user, require_admin
from ..admin.deployment_manager import (
    deployment_manager, 
    UserProvisioningRequest, 
    DeploymentConfig,
    SubscriptionTier,
    DeploymentStatus
)
from ..schemas.admin import (
    AdminDashboardResponse,
    UserProvisioningResponse,
    DeploymentStatsResponse,
    TenantListResponse,
    DeploymentInstanceResponse,
    BulkProvisioningRequest,
    ScaleDeploymentRequest,
    SuspendDeploymentRequest
)

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/admin", tags=["admin"])
security = HTTPBearer()


@router.get("/dashboard", response_model=AdminDashboardResponse)
async def get_admin_dashboard(
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get admin dashboard overview"""
    try:
        # Get deployment statistics
        deployment_stats = await deployment_manager.get_deployment_stats()
        
        # Get user statistics
        total_users = await db.execute(select(func.count(User.id)))
        active_users = await db.execute(
            select(func.count(User.id)).where(User.is_active == True)
        )
        
        # Get recent activity
        recent_deployments = await db.execute(
            select(DeploymentInstance)
            .order_by(desc(DeploymentInstance.created_at))
            .limit(10)
        )
        
        recent_users = await db.execute(
            select(User)
            .order_by(desc(User.created_at))
            .limit(10)
        )
        
        # Get system health
        system_health = await _get_system_health(db)
        
        return AdminDashboardResponse(
            total_users=total_users.scalar(),
            active_users=active_users.scalar(),
            total_deployments=deployment_stats['total_deployments'],
            active_deployments=deployment_stats['status_distribution'].get('active', 0),
            deployment_stats=deployment_stats,
            recent_deployments=[
                DeploymentInstanceResponse.from_orm(d) for d in recent_deployments.scalars()
            ],
            recent_users=[
                {
                    'id': u.id,
                    'email': u.email,
                    'full_name': u.full_name,
                    'created_at': u.created_at,
                    'subscription_tier': u.subscription_tier
                } for u in recent_users.scalars()
            ],
            system_health=system_health
        )
        
    except Exception as e:
        logger.error(f"Failed to get admin dashboard: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to load dashboard"
        )


@router.post("/users/provision", response_model=UserProvisioningResponse)
async def provision_user(
    request: UserProvisioningRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Provision a new user with automatic deployment"""
    try:
        result = await deployment_manager.provision_user(request, current_user.id)
        
        # Add background task for post-provisioning setup
        background_tasks.add_task(
            _post_provisioning_setup,
            result['tenant_id'],
            result['user_id']
        )
        
        return UserProvisioningResponse(**result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to provision user: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to provision user"
        )


@router.post("/users/bulk-provision")
async def bulk_provision_users(
    request: BulkProvisioningRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Bulk provision multiple users"""
    try:
        # Validate request
        if len(request.users) > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maximum 100 users can be provisioned at once"
            )
        
        # Start bulk provisioning in background
        background_tasks.add_task(
            _bulk_provision_background,
            request.users,
            current_user.id,
            request.notify_admin
        )
        
        return {
            'message': f'Bulk provisioning started for {len(request.users)} users',
            'total_users': len(request.users),
            'status': 'processing'
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to start bulk provisioning: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start bulk provisioning"
        )


@router.get("/tenants", response_model=TenantListResponse)
async def list_tenants(
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    status: Optional[str] = Query(None),
    tier: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """List all tenants with filtering"""
    try:
        query = select(Tenant)
        
        # Apply filters
        if status:
            query = query.where(Tenant.status == status)
        if tier:
            query = query.where(Tenant.subscription_tier == tier)
        if search:
            query = query.where(
                or_(
                    Tenant.name.ilike(f"%{search}%"),
                    Tenant.domain.ilike(f"%{search}%")
                )
            )
        
        # Get total count
        total_query = select(func.count()).select_from(query.subquery())
        total = await db.execute(total_query)
        total_count = total.scalar()
        
        # Apply pagination
        query = query.offset(skip).limit(limit).order_by(desc(Tenant.created_at))
        result = await db.execute(query)
        tenants = result.scalars().all()
        
        return TenantListResponse(
            tenants=[
                {
                    'id': t.id,
                    'uuid': t.uuid,
                    'name': t.name,
                    'domain': t.domain,
                    'status': t.status.value,
                    'subscription_tier': t.subscription_tier.value,
                    'created_at': t.created_at,
                    'user_count': len(t.users) if t.users else 0
                } for t in tenants
            ],
            total=total_count,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Failed to list tenants: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list tenants"
        )


@router.get("/deployments/stats", response_model=DeploymentStatsResponse)
async def get_deployment_stats(
    current_user: User = Depends(require_admin)
):
    """Get comprehensive deployment statistics"""
    try:
        stats = await deployment_manager.get_deployment_stats()
        return DeploymentStatsResponse(**stats)
        
    except Exception as e:
        logger.error(f"Failed to get deployment stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get deployment statistics"
        )


@router.post("/deployments/{tenant_id}/scale")
async def scale_deployment(
    tenant_id: str,
    request: ScaleDeploymentRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Scale deployment to new subscription tier"""
    try:
        result = await deployment_manager.scale_deployment(
            tenant_id,
            SubscriptionTier(request.new_tier),
            current_user.id
        )
        
        return {
            'message': 'Deployment scaled successfully',
            'result': result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to scale deployment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to scale deployment"
        )


@router.post("/deployments/{tenant_id}/suspend")
async def suspend_deployment(
    tenant_id: str,
    request: SuspendDeploymentRequest,
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Suspend a deployment"""
    try:
        result = await deployment_manager.suspend_deployment(
            tenant_id,
            request.reason
        )
        
        # Log the suspension
        await _log_deployment_action(
            db,
            tenant_id,
            'suspend',
            'success',
            f"Suspended by admin: {request.reason}",
            current_user.id
        )
        
        return {
            'message': 'Deployment suspended successfully',
            'result': result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to suspend deployment: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to suspend deployment"
        )


@router.get("/deployments/{tenant_id}/logs")
async def get_deployment_logs(
    tenant_id: str,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get deployment logs for a tenant"""
    try:
        # Get deployment instance
        deployment = await db.execute(
            select(DeploymentInstance).where(
                DeploymentInstance.tenant_id == tenant_id
            )
        )
        deployment = deployment.scalar_one_or_none()
        
        if not deployment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deployment not found"
            )
        
        # Get logs
        logs_query = select(DeploymentLog).where(
            DeploymentLog.deployment_id == deployment.id
        ).order_by(desc(DeploymentLog.created_at)).offset(skip).limit(limit)
        
        logs = await db.execute(logs_query)
        
        return {
            'logs': [
                {
                    'id': log.id,
                    'operation': log.operation,
                    'status': log.status,
                    'message': log.message,
                    'details': log.details,
                    'started_at': log.started_at,
                    'completed_at': log.completed_at,
                    'duration_seconds': log.duration_seconds
                } for log in logs.scalars()
            ],
            'total': len(list(logs.scalars())),
            'skip': skip,
            'limit': limit
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get deployment logs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get deployment logs"
        )


@router.get("/usage/{tenant_id}")
async def get_tenant_usage(
    tenant_id: str,
    days: int = Query(30, ge=1, le=365),
    current_user: User = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Get usage metrics for a tenant"""
    try:
        # Get tenant
        tenant = await db.execute(
            select(Tenant).where(Tenant.uuid == tenant_id)
        )
        tenant = tenant.scalar_one_or_none()
        
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Get usage metrics
        start_date = datetime.utcnow() - timedelta(days=days)
        usage_query = select(UsageMetrics).where(
            and_(
                UsageMetrics.tenant_id == tenant.id,
                UsageMetrics.period_start >= start_date
            )
        ).order_by(UsageMetrics.period_start)
        
        usage_metrics = await db.execute(usage_query)
        
        # Aggregate metrics by type
        metrics_by_type = {}
        for metric in usage_metrics.scalars():
            if metric.metric_type not in metrics_by_type:
                metrics_by_type[metric.metric_type] = []
            
            metrics_by_type[metric.metric_type].append({
                'value': metric.value,
                'unit': metric.unit,
                'period_start': metric.period_start,
                'period_end': metric.period_end
            })
        
        return {
            'tenant_id': tenant_id,
            'tenant_name': tenant.name,
            'period_days': days,
            'metrics': metrics_by_type
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get tenant usage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get tenant usage"
        )


# Background task functions
async def _post_provisioning_setup(tenant_id: str, user_id: int):
    """Post-provisioning setup tasks"""
    try:
        # Setup default configurations, send notifications, etc.
        logger.info(f"Post-provisioning setup completed for tenant {tenant_id}")
    except Exception as e:
        logger.error(f"Post-provisioning setup failed: {str(e)}")


async def _bulk_provision_background(
    users: List[UserProvisioningRequest],
    admin_user_id: int,
    notify_admin: bool
):
    """Background task for bulk provisioning"""
    try:
        result = await deployment_manager.bulk_provision_users(users, admin_user_id)
        
        if notify_admin:
            # Send notification to admin about completion
            logger.info(f"Bulk provisioning completed: {result}")
            
    except Exception as e:
        logger.error(f"Bulk provisioning failed: {str(e)}")


async def _get_system_health(db: AsyncSession) -> Dict[str, Any]:
    """Get system health metrics"""
    try:
        # Check database connectivity
        await db.execute(select(1))
        db_status = "healthy"
        
        # Check deployment manager
        dm_status = "healthy" if deployment_manager else "error"
        
        return {
            'database': db_status,
            'deployment_manager': dm_status,
            'overall': 'healthy' if db_status == 'healthy' and dm_status == 'healthy' else 'degraded'
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return {
            'database': 'error',
            'deployment_manager': 'error',
            'overall': 'error'
        }


async def _log_deployment_action(
    db: AsyncSession,
    tenant_id: str,
    operation: str,
    status: str,
    message: str,
    user_id: int
):
    """Log deployment action"""
    try:
        # Get deployment instance
        deployment = await db.execute(
            select(DeploymentInstance).where(
                DeploymentInstance.tenant_id == tenant_id
            )
        )
        deployment = deployment.scalar_one_or_none()
        
        if deployment:
            log_entry = DeploymentLog(
                deployment_id=deployment.id,
                operation=operation,
                status=status,
                message=message,
                started_at=datetime.utcnow(),
                completed_at=datetime.utcnow(),
                initiated_by=user_id
            )
            db.add(log_entry)
            await db.commit()
            
    except Exception as e:
        logger.error(f"Failed to log deployment action: {str(e)}")
