# Legal AI Bot - Docker Compose Configuration
# Production-ready containerized deployment

version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: legal-ai-bot-db
    restart: unless-stopped
    environment:
      POSTGRES_DB: legal_ai_bot
      POSTGRES_USER: legal_ai_user
      POSTGRES_PASSWORD: ${DB_PASSWORD:-secure_password_change_me}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - legal-ai-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U legal_ai_user -d legal_ai_bot"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for Caching and Background Tasks
  redis:
    image: redis:7-alpine
    container_name: legal-ai-bot-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_change_me}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - legal-ai-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: legal-ai-bot-backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://legal_ai_user:${DB_PASSWORD:-secure_password_change_me}@postgres:5432/legal_ai_bot
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
      - HOST=0.0.0.0
      - PORT=8000
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./vector_db:/app/vector_db
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - legal-ai-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: legal-ai-bot-nginx
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./logs/nginx:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    networks:
      - legal-ai-network

  # Background Task Worker (Celery)
  worker:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: legal-ai-bot-worker
    restart: unless-stopped
    command: celery -A backend.core.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql://legal_ai_user:${DB_PASSWORD:-secure_password_change_me}@postgres:5432/legal_ai_bot
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./vector_db:/app/vector_db
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - legal-ai-network

  # Task Scheduler (Celery Beat)
  scheduler:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: legal-ai-bot-scheduler
    restart: unless-stopped
    command: celery -A backend.core.celery_app beat --loglevel=info
    environment:
      - DATABASE_URL=postgresql://legal_ai_user:${DB_PASSWORD:-secure_password_change_me}@postgres:5432/legal_ai_bot
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379
      - SECRET_KEY=${SECRET_KEY}
    volumes:
      - ./logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - legal-ai-network

  # Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: legal-ai-bot-prometheus
    restart: unless-stopped
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - legal-ai-network

  # Grafana for Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: legal-ai-bot-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin_password_change_me}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    ports:
      - "3001:3000"
    depends_on:
      - prometheus
    networks:
      - legal-ai-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  legal-ai-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
