{"name": "legal-ai-bot-desktop", "version": "1.0.0", "description": "Professional Legal AI Assistant - Desktop Application", "main": "electron.js", "homepage": "./", "private": true, "author": {"name": "Legal AI Bot Team", "email": "<EMAIL>"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-build": "npm run build && electron .", "dist": "npm run build && electron-builder", "dist-mac": "npm run build && electron-builder --mac", "dist-win": "npm run build && electron-builder --win", "dist-linux": "npm run build && electron-builder --linux", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.19", "@mui/material": "^5.14.20", "@mui/x-data-grid": "^6.18.2", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "dompurify": "^3.0.6", "electron-store": "^8.1.0", "file-saver": "^2.0.5", "mammoth": "^1.6.0", "marked": "^11.1.1", "pdf-lib": "^1.17.1", "pdfjs-dist": "^3.11.174", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-markdown": "^9.0.1", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "react-syntax-highlighter": "^15.5.0", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "typescript": "^4.9.5", "uuid": "^9.0.1", "web-vitals": "^3.5.0"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/file-saver": "^2.0.7", "@types/jest": "^27.5.2", "@types/marked": "^6.0.0", "@types/node": "^16.18.68", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/react-syntax-highlighter": "^15.5.11", "@types/uuid": "^9.0.7", "concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.8.1", "wait-on": "^7.2.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.legalaibot.desktop", "productName": "Legal AI Bot", "directories": {"output": "dist"}, "files": ["build/**/*", "electron.js", "preload.js", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity", "icon": "build/icons/icon.icns", "hardenedRuntime": true, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": "nsis", "icon": "build/icons/icon.ico"}, "linux": {"target": "AppImage", "icon": "build/icons/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}