/**
 * File Service for Large File Upload Management
 * Handles chunked uploads, progress tracking, and file processing
 */

import { apiClient } from './api';

export interface ChunkedUploadRequest {
  filename: string;
  file_size: number;
  content_type: string;
  case_id?: string;
  process_immediately?: boolean;
}

export interface ChunkedUploadResponse {
  file_id: string;
  upload_url: string;
  chunk_size: number;
  max_chunks: number;
}

export interface UploadProgress {
  file_id: string;
  filename: string;
  total_size: number;
  uploaded_size: number;
  progress_percentage: number;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  error_message?: string;
}

export interface FileProcessingResult {
  file_id: string;
  filename: string;
  file_size: number;
  content_type: string;
  extracted_text: string;
  page_count?: number;
  processing_time: number;
  metadata: Record<string, any>;
  chunks_created: number;
  embeddings_created: boolean;
  error?: string;
}

export interface DocumentInfo {
  id: string;
  filename: string;
  file_size: number;
  content_type: string;
  case_id?: string;
  extracted_text_preview: string;
  page_count?: number;
  processing_status: string;
  created_at: string;
  updated_at: string;
}

class FileService {
  private baseUrl = '/api/files';

  /**
   * Start a chunked upload for large files
   */
  async startChunkedUpload(request: ChunkedUploadRequest): Promise<ChunkedUploadResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/upload/start`, request);
      return response.data;
    } catch (error) {
      console.error('Failed to start chunked upload:', error);
      throw error;
    }
  }

  /**
   * Upload a file chunk
   */
  async uploadChunk(fileId: string, formData: FormData): Promise<UploadProgress> {
    try {
      const response = await apiClient.post(
        `${this.baseUrl}/upload/chunk/${fileId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Failed to upload chunk:', error);
      throw error;
    }
  }

  /**
   * Complete file upload and process
   */
  async completeUpload(
    fileId: string,
    caseId?: string,
    processImmediately: boolean = true
  ): Promise<FileProcessingResult> {
    try {
      const formData = new FormData();
      if (caseId) formData.append('case_id', caseId);
      formData.append('process_immediately', processImmediately.toString());

      const response = await apiClient.post(
        `${this.baseUrl}/upload/complete/${fileId}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        }
      );
      return response.data;
    } catch (error) {
      console.error('Failed to complete upload:', error);
      throw error;
    }
  }

  /**
   * Get upload progress
   */
  async getUploadProgress(fileId: string): Promise<UploadProgress> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/upload/progress/${fileId}`);
      return response.data;
    } catch (error) {
      console.error('Failed to get upload progress:', error);
      throw error;
    }
  }

  /**
   * Get all active uploads
   */
  async getActiveUploads(): Promise<UploadProgress[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/upload/active`);
      return response.data;
    } catch (error) {
      console.error('Failed to get active uploads:', error);
      throw error;
    }
  }

  /**
   * Simple upload for smaller files
   */
  async simpleUpload(file: File, caseId?: string): Promise<FileProcessingResult> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (caseId) formData.append('case_id', caseId);

      const response = await apiClient.post(`${this.baseUrl}/upload/simple`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Failed to upload file:', error);
      throw error;
    }
  }

  /**
   * Get user documents
   */
  async getUserDocuments(
    limit: number = 50,
    offset: number = 0,
    caseId?: string,
    contentType?: string
  ): Promise<DocumentInfo[]> {
    try {
      const params: Record<string, any> = { limit, offset };
      if (caseId) params.case_id = caseId;
      if (contentType) params.content_type = contentType;

      const response = await apiClient.get(`${this.baseUrl}/documents`, { params });
      return response.data;
    } catch (error) {
      console.error('Failed to get documents:', error);
      throw error;
    }
  }

  /**
   * Delete a document
   */
  async deleteDocument(documentId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/documents/${documentId}`);
    } catch (error) {
      console.error('Failed to delete document:', error);
      throw error;
    }
  }

  /**
   * Reprocess a document
   */
  async reprocessDocument(documentId: string): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/documents/${documentId}/reprocess`);
    } catch (error) {
      console.error('Failed to reprocess document:', error);
      throw error;
    }
  }

  /**
   * Upload file with progress tracking
   */
  async uploadFileWithProgress(
    file: File,
    caseId?: string,
    processImmediately: boolean = true,
    onProgress?: (progress: number) => void
  ): Promise<FileProcessingResult> {
    const maxSimpleSize = 100 * 1024 * 1024; // 100MB

    if (file.size <= maxSimpleSize) {
      // Use simple upload for smaller files
      return this.simpleUpload(file, caseId);
    }

    // Use chunked upload for larger files
    const uploadResponse = await this.startChunkedUpload({
      filename: file.name,
      file_size: file.size,
      content_type: file.type,
      case_id: caseId,
      process_immediately: processImmediately,
    });

    const chunkSize = uploadResponse.chunk_size;
    const totalChunks = Math.ceil(file.size / chunkSize);

    // Upload chunks
    for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
      const start = chunkIndex * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      const formData = new FormData();
      formData.append('chunk_data', chunk);
      formData.append('chunk_offset', start.toString());

      await this.uploadChunk(uploadResponse.file_id, formData);

      // Report progress
      if (onProgress) {
        const progress = ((chunkIndex + 1) / totalChunks) * 90; // Reserve 10% for processing
        onProgress(progress);
      }
    }

    // Complete upload
    if (onProgress) onProgress(90);
    const result = await this.completeUpload(uploadResponse.file_id, caseId, processImmediately);
    if (onProgress) onProgress(100);

    return result;
  }

  /**
   * Get supported file types
   */
  getSupportedFileTypes(): string[] {
    return [
      'application/pdf',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'text/plain',
      'text/rtf',
      'image/jpeg',
      'image/png',
      'image/tiff',
    ];
  }

  /**
   * Check if file type is supported
   */
  isFileTypeSupported(fileType: string): boolean {
    return this.getSupportedFileTypes().includes(fileType);
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Get file type icon name
   */
  getFileTypeIcon(contentType: string): string {
    if (contentType.includes('pdf')) return 'picture_as_pdf';
    if (contentType.includes('word') || contentType.includes('document')) return 'description';
    if (contentType.includes('image')) return 'image';
    if (contentType.includes('text')) return 'text_snippet';
    return 'insert_drive_file';
  }

  /**
   * Validate file before upload
   */
  validateFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    if (!this.isFileTypeSupported(file.type)) {
      return {
        valid: false,
        error: `File type ${file.type} is not supported`,
      };
    }

    // Check file size (10GB max)
    const maxSize = 10 * 1024 * 1024 * 1024;
    if (file.size > maxSize) {
      return {
        valid: false,
        error: `File size ${this.formatFileSize(file.size)} exceeds maximum ${this.formatFileSize(maxSize)}`,
      };
    }

    return { valid: true };
  }
}

export const fileService = new FileService();
