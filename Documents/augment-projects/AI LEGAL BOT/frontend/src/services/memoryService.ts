/**
 * Memory Service for Cross-Conversation Context Management
 * Handles global memory, context retrieval, and conversation continuity
 */

import { apiClient } from './api';

export interface MemoryContext {
  relevant_messages: Array<{
    content: string;
    role: string;
    timestamp: string;
    conversation_id: string;
    practice_area: string;
    case_id: string;
    relevance_score: number;
  }>;
  conversation_summaries: Array<{
    id: string;
    title: string;
    summary: string;
    key_topics: string[];
    practice_areas: string[];
    created_at: string;
  }>;
  key_topics: string[];
  related_cases: string[];
  confidence_score: number;
  total_context_length: number;
}

export interface ConversationContinuation {
  previous_conversation: Array<{
    id: string;
    role: string;
    content: string;
    timestamp: string;
  }>;
  relevant_history: Array<{
    content: string;
    role: string;
    conversation_id: string;
    relevance_score: number;
  }>;
  conversation_summaries: Array<{
    id: string;
    title: string;
    summary: string;
    key_topics: string[];
  }>;
  key_topics: string[];
  related_cases: string[];
  context_confidence: number;
  continuation_prompt: string;
}

export interface SearchResult {
  content: string;
  metadata: {
    conversation_id: string;
    message_role: string;
    timestamp: string;
    practice_area: string;
    case_id: string;
  };
  relevance_score: number;
  conversation_id: string;
}

export interface SearchFilters {
  practice_area?: string;
  case_id?: string;
  message_type?: string;
  date_from?: string;
  date_to?: string;
}

class MemoryService {
  private baseUrl = '/api/memory';

  /**
   * Get relevant context for a query across all conversations
   */
  async getRelevantContext(
    query: string,
    currentConversationId?: string,
    maxResults: number = 20,
    includeAllConversations: boolean = true
  ): Promise<MemoryContext> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/context`, {
        query,
        current_conversation_id: currentConversationId,
        max_results: maxResults,
        include_all_conversations: includeAllConversations
      });

      return response.data;
    } catch (error) {
      console.error('Failed to get relevant context:', error);
      throw error;
    }
  }

  /**
   * Create a new conversation with full context from previous conversations
   */
  async createConversationContinuation(
    contextQuery: string,
    previousConversationId?: string
  ): Promise<ConversationContinuation> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/continuation`, {
        context_query: contextQuery,
        previous_conversation_id: previousConversationId
      });

      return response.data;
    } catch (error) {
      console.error('Failed to create conversation continuation:', error);
      throw error;
    }
  }

  /**
   * Search across all conversations with advanced filtering
   */
  async searchAllConversations(
    query: string,
    filters?: SearchFilters,
    limit: number = 50
  ): Promise<SearchResult[]> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/search`, {
        query,
        filters,
        limit
      });

      return response.data;
    } catch (error) {
      console.error('Failed to search conversations:', error);
      throw error;
    }
  }

  /**
   * Get conversation history with enhanced context
   */
  async getConversationHistory(
    conversationId: string,
    limit: number = 50,
    includeContext: boolean = true
  ): Promise<Array<{
    id: string;
    role: string;
    content: string;
    timestamp: string;
    metadata: any;
    relevant_context?: {
      messages: Array<{
        content: string;
        conversation_id: string;
        relevance_score: number;
      }>;
      topics: string[];
      cases: string[];
    };
  }>> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/conversations/${conversationId}/history`,
        {
          params: {
            limit,
            include_context: includeContext
          }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get conversation history:', error);
      throw error;
    }
  }

  /**
   * Get conversation summaries for quick context overview
   */
  async getConversationSummaries(
    limit: number = 20,
    practiceArea?: string,
    caseId?: string
  ): Promise<Array<{
    id: string;
    title: string;
    summary: string;
    key_topics: string[];
    practice_areas: string[];
    message_count: number;
    created_at: string;
    updated_at: string;
  }>> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/summaries`, {
        params: {
          limit,
          practice_area: practiceArea,
          case_id: caseId
        }
      });

      return response.data;
    } catch (error) {
      console.error('Failed to get conversation summaries:', error);
      throw error;
    }
  }

  /**
   * Get related conversations based on content similarity
   */
  async getRelatedConversations(
    conversationId: string,
    limit: number = 10
  ): Promise<Array<{
    id: string;
    title: string;
    similarity_score: number;
    shared_topics: string[];
    practice_area: string;
    created_at: string;
  }>> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/conversations/${conversationId}/related`,
        {
          params: { limit }
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to get related conversations:', error);
      throw error;
    }
  }

  /**
   * Update conversation memory settings
   */
  async updateMemorySettings(
    conversationId: string,
    settings: {
      memory_enabled?: boolean;
      cross_conversation_context?: boolean;
      max_context_messages?: number;
    }
  ): Promise<void> {
    try {
      await apiClient.patch(`${this.baseUrl}/conversations/${conversationId}/settings`, settings);
    } catch (error) {
      console.error('Failed to update memory settings:', error);
      throw error;
    }
  }

  /**
   * Get memory statistics and insights
   */
  async getMemoryStats(): Promise<{
    total_conversations: number;
    total_messages: number;
    total_context_length: number;
    top_topics: Array<{ topic: string; count: number }>;
    practice_area_distribution: Array<{ area: string; count: number }>;
    memory_usage: {
      vector_db_size: number;
      embeddings_count: number;
      cache_size: number;
    };
  }> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/stats`);
      return response.data;
    } catch (error) {
      console.error('Failed to get memory stats:', error);
      throw error;
    }
  }

  /**
   * Export conversation data with full context
   */
  async exportConversationData(
    conversationIds: string[],
    includeContext: boolean = true,
    format: 'json' | 'pdf' | 'docx' = 'json'
  ): Promise<Blob> {
    try {
      const response = await apiClient.post(
        `${this.baseUrl}/export`,
        {
          conversation_ids: conversationIds,
          include_context: includeContext,
          format
        },
        {
          responseType: 'blob'
        }
      );

      return response.data;
    } catch (error) {
      console.error('Failed to export conversation data:', error);
      throw error;
    }
  }

  /**
   * Clear memory cache (admin function)
   */
  async clearMemoryCache(): Promise<void> {
    try {
      await apiClient.post(`${this.baseUrl}/cache/clear`);
    } catch (error) {
      console.error('Failed to clear memory cache:', error);
      throw error;
    }
  }

  /**
   * Rebuild conversation embeddings (admin function)
   */
  async rebuildEmbeddings(conversationIds?: string[]): Promise<{
    task_id: string;
    estimated_time: number;
  }> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/embeddings/rebuild`, {
        conversation_ids: conversationIds
      });

      return response.data;
    } catch (error) {
      console.error('Failed to rebuild embeddings:', error);
      throw error;
    }
  }
}

export const memoryService = new MemoryService();
