/**
 * Legal AI Bot - Main Application Component
 * Professional desktop interface for legal AI assistance
 */

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline, Box, Snackbar, Alert } from '@mui/material';
import { Provider } from 'react-redux';

import { store } from './store/store';
import { useAppDispatch, useAppSelector } from './hooks/redux';
import { setUser, setTheme } from './store/slices/appSlice';

// Components
import Sidebar from './components/Common/Sidebar';
import TopBar from './components/Common/TopBar';
import LoadingScreen from './components/Common/LoadingScreen';
import ErrorBoundary from './components/Common/ErrorBoundary';

// Pages
import ChatPage from './pages/ChatPage';
import CasesPage from './pages/CasesPage';
import DocumentsPage from './pages/DocumentsPage';
import SettingsPage from './pages/SettingsPage';
import LoginPage from './pages/LoginPage';

// Services
import { authService } from './services/auth';
import { electronService } from './services/electron';

// Types
import { User, AppTheme } from './types/app';

// Styles
import './styles/globals.css';

const AppContent: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user, theme, isLoading } = useAppSelector((state) => state.app);
  
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Create MUI theme based on app theme
  const muiTheme = createTheme({
    palette: {
      mode: theme === 'dark' ? 'dark' : 'light',
      primary: {
        main: '#1976d2',
        dark: '#115293',
        light: '#42a5f5'
      },
      secondary: {
        main: '#dc004e',
        dark: '#9a0036',
        light: '#e33371'
      },
      background: {
        default: theme === 'dark' ? '#121212' : '#fafafa',
        paper: theme === 'dark' ? '#1e1e1e' : '#ffffff'
      },
      text: {
        primary: theme === 'dark' ? '#ffffff' : '#000000',
        secondary: theme === 'dark' ? '#b3b3b3' : '#666666'
      }
    },
    typography: {
      fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
      h1: {
        fontSize: '2.5rem',
        fontWeight: 600,
        lineHeight: 1.2
      },
      h2: {
        fontSize: '2rem',
        fontWeight: 600,
        lineHeight: 1.3
      },
      h3: {
        fontSize: '1.5rem',
        fontWeight: 600,
        lineHeight: 1.4
      },
      body1: {
        fontSize: '1rem',
        lineHeight: 1.6
      },
      body2: {
        fontSize: '0.875rem',
        lineHeight: 1.5
      }
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            textTransform: 'none',
            borderRadius: 8,
            fontWeight: 500
          }
        }
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            borderRadius: 12
          }
        }
      },
      MuiCard: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            boxShadow: theme === 'dark' 
              ? '0 4px 6px rgba(0, 0, 0, 0.3)' 
              : '0 4px 6px rgba(0, 0, 0, 0.1)'
          }
        }
      }
    }
  });

  // Initialize app
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Load saved theme
        const savedTheme = await electronService.getStoredValue('theme') as AppTheme;
        if (savedTheme) {
          dispatch(setTheme(savedTheme));
        }

        // Check for existing authentication
        const token = await electronService.getStoredValue('authToken');
        if (token) {
          try {
            const userData = await authService.validateToken(token);
            dispatch(setUser(userData));
          } catch (error) {
            // Token invalid, remove it
            await electronService.removeStoredValue('authToken');
          }
        }
      } catch (error) {
        console.error('App initialization error:', error);
        showNotification('Failed to initialize application', 'error');
      }
    };

    initializeApp();
  }, [dispatch]);

  // Handle Electron menu events
  useEffect(() => {
    const handleMenuEvents = () => {
      if (window.electronAPI) {
        window.electronAPI.onMenuNewCase(() => {
          // Navigate to new case creation
          window.location.hash = '/cases/new';
        });

        window.electronAPI.onMenuExportChat(() => {
          showNotification('Export functionality coming soon', 'info');
        });

        window.electronAPI.onMenuDocumentTemplates(() => {
          window.location.hash = '/documents/templates';
        });

        window.electronAPI.onMenuCitationHelper(() => {
          showNotification('Citation helper coming soon', 'info');
        });

        window.electronAPI.onMenuLegalResearch(() => {
          window.location.hash = '/research';
        });

        window.electronAPI.onMenuLegalDisclaimer(() => {
          showNotification('Please review the legal disclaimer in Settings', 'warning');
        });

        window.electronAPI.onFilesSelected((filePaths: string[]) => {
          // Handle file selection from menu
          console.log('Files selected:', filePaths);
          showNotification(`${filePaths.length} file(s) selected for processing`, 'info');
        });
      }
    };

    handleMenuEvents();
  }, []);

  const showNotification = (message: string, severity: 'success' | 'error' | 'warning' | 'info') => {
    setNotification({
      open: true,
      message,
      severity
    });
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  if (isLoading) {
    return <LoadingScreen />;
  }

  if (!user) {
    return <LoginPage onLogin={(userData) => dispatch(setUser(userData))} />;
  }

  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* Sidebar */}
      <Sidebar 
        open={sidebarOpen} 
        onToggle={toggleSidebar}
        user={user}
      />

      {/* Main Content Area */}
      <Box 
        component="main" 
        sx={{ 
          flexGrow: 1, 
          display: 'flex', 
          flexDirection: 'column',
          overflow: 'hidden',
          marginLeft: sidebarOpen ? 0 : '-280px',
          transition: 'margin-left 0.3s ease'
        }}
      >
        {/* Top Bar */}
        <TopBar 
          onToggleSidebar={toggleSidebar}
          sidebarOpen={sidebarOpen}
          user={user}
        />

        {/* Page Content */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 3 }}>
          <Routes>
            <Route path="/" element={<Navigate to="/chat" replace />} />
            <Route path="/chat" element={<ChatPage />} />
            <Route path="/chat/:conversationId" element={<ChatPage />} />
            <Route path="/cases" element={<CasesPage />} />
            <Route path="/cases/:caseId" element={<CasesPage />} />
            <Route path="/documents" element={<DocumentsPage />} />
            <Route path="/documents/:documentId" element={<DocumentsPage />} />
            <Route path="/settings" element={<SettingsPage />} />
            <Route path="*" element={<Navigate to="/chat" replace />} />
          </Routes>
        </Box>
      </Box>

      {/* Global Notification */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert 
          onClose={handleCloseNotification} 
          severity={notification.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

const App: React.FC = () => {
  return (
    <Provider store={store}>
      <Router>
        <ThemeProvider theme={createTheme()}>
          <CssBaseline />
          <ErrorBoundary>
            <AppContent />
          </ErrorBoundary>
        </ThemeProvider>
      </Router>
    </Provider>
  );
};

export default App;
