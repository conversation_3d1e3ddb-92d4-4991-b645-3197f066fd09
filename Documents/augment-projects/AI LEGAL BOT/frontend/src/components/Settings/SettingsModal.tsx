/**
 * Comprehensive Settings Modal - Strategic Organization
 * Professional settings interface with organized tabs and advanced options
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  Box,
  Typography,
  TextField,
  Switch,
  FormControlLabel,
  FormGroup,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Divider,
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  LinearProgress,
  Grid,
  Avatar,
  Badge
} from '@mui/material';
import {
  Close,
  Save,
  Restore,
  Security,
  Psychology,
  Memory,
  CloudUpload,
  Notifications,
  Palette,
  Language,
  Storage,
  Analytics,
  CreditCard,
  AdminPanelSettings,
  Delete,
  Edit,
  Visibility,
  VisibilityOff
} from '@mui/icons-material';

import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { updateUserSettings, updateAISettings } from '../../store/slices/settingsSlice';

interface SettingsModalProps {
  open: boolean;
  onClose: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`settings-tabpanel-${index}`}
    aria-labelledby={`settings-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
  </div>
);

export const SettingsModal: React.FC<SettingsModalProps> = ({ open, onClose }) => {
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const { userSettings, aiSettings, memorySettings } = useAppSelector(state => state.settings);

  const [currentTab, setCurrentTab] = useState(0);
  const [localSettings, setLocalSettings] = useState({
    // Account settings
    fullName: user?.full_name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    barNumber: user?.bar_number || '',
    lawFirm: user?.law_firm || '',
    practiceAreas: user?.practice_areas || [],
    jurisdiction: user?.jurisdiction || '',
    
    // AI preferences
    temperature: aiSettings?.temperature || 0.1,
    maxTokens: aiSettings?.max_tokens || 4000,
    responseStyle: aiSettings?.response_style || 'professional',
    citationStyle: aiSettings?.citation_style || 'bluebook',
    confidenceThreshold: aiSettings?.confidence_threshold || 0.8,
    
    // Memory settings
    memoryEnabled: memorySettings?.enabled || true,
    crossConversationContext: memorySettings?.cross_conversation || true,
    maxContextMessages: memorySettings?.max_context_messages || 50,
    autoSummarize: memorySettings?.auto_summarize || true,
    
    // UI preferences
    theme: userSettings?.theme || 'light',
    language: userSettings?.language || 'en',
    fontSize: userSettings?.font_size || 'medium',
    sidebarCollapsed: userSettings?.sidebar_collapsed || false,
    
    // Notifications
    emailNotifications: userSettings?.email_notifications || true,
    pushNotifications: userSettings?.push_notifications || true,
    soundEnabled: userSettings?.sound_enabled || true,
    
    // File processing
    autoProcess: userSettings?.auto_process_files || true,
    ocrEnabled: userSettings?.ocr_enabled || true,
    maxFileSize: userSettings?.max_file_size || 10,
    
    // Security
    twoFactorEnabled: user?.two_factor_enabled || false,
    sessionTimeout: userSettings?.session_timeout || 60,
    loginRequired: userSettings?.login_required || true
  });

  const [showPassword, setShowPassword] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  // Tab configuration
  const tabs = [
    { label: 'Account', icon: <AdminPanelSettings /> },
    { label: 'AI Assistant', icon: <Psychology /> },
    { label: 'Memory', icon: <Memory /> },
    { label: 'Interface', icon: <Palette /> },
    { label: 'Files', icon: <CloudUpload /> },
    { label: 'Security', icon: <Security /> },
    { label: 'Notifications', icon: <Notifications /> },
    { label: 'Billing', icon: <CreditCard /> }
  ];

  // Practice areas options
  const practiceAreas = [
    'Corporate Law', 'Litigation', 'Real Estate', 'Family Law',
    'Criminal Law', 'Employment Law', 'Intellectual Property',
    'Tax Law', 'Immigration Law', 'Bankruptcy Law'
  ];

  // Handle setting changes
  const handleSettingChange = (key: string, value: any) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  // Save settings
  const handleSave = async () => {
    try {
      // Dispatch updates to different slices
      dispatch(updateUserSettings({
        theme: localSettings.theme,
        language: localSettings.language,
        fontSize: localSettings.fontSize,
        sidebarCollapsed: localSettings.sidebarCollapsed,
        emailNotifications: localSettings.emailNotifications,
        pushNotifications: localSettings.pushNotifications,
        soundEnabled: localSettings.soundEnabled,
        autoProcessFiles: localSettings.autoProcess,
        ocrEnabled: localSettings.ocrEnabled,
        maxFileSize: localSettings.maxFileSize,
        sessionTimeout: localSettings.sessionTimeout,
        loginRequired: localSettings.loginRequired
      }));

      dispatch(updateAISettings({
        temperature: localSettings.temperature,
        maxTokens: localSettings.maxTokens,
        responseStyle: localSettings.responseStyle,
        citationStyle: localSettings.citationStyle,
        confidenceThreshold: localSettings.confidenceThreshold
      }));

      setHasChanges(false);
      onClose();
    } catch (error) {
      console.error('Failed to save settings:', error);
    }
  };

  // Reset to defaults
  const handleReset = () => {
    // Reset logic here
    setHasChanges(false);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '90vh', maxHeight: 800 }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
        <Typography variant="h5" fontWeight="bold">
          Settings
        </Typography>
        <IconButton onClick={onClose}>
          <Close />
        </IconButton>
      </DialogTitle>

      <Box sx={{ display: 'flex', height: '100%' }}>
        {/* Sidebar with tabs */}
        <Box sx={{ width: 200, borderRight: 1, borderColor: 'divider' }}>
          <Tabs
            orientation="vertical"
            value={currentTab}
            onChange={(_, newValue) => setCurrentTab(newValue)}
            sx={{ '& .MuiTab-root': { alignItems: 'flex-start', textAlign: 'left' } }}
          >
            {tabs.map((tab, index) => (
              <Tab
                key={index}
                icon={tab.icon}
                label={tab.label}
                iconPosition="start"
                sx={{ justifyContent: 'flex-start', minHeight: 60 }}
              />
            ))}
          </Tabs>
        </Box>

        {/* Content area */}
        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <DialogContent>
            {/* Account Settings */}
            <TabPanel value={currentTab} index={0}>
              <Typography variant="h6" gutterBottom>Account Information</Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Full Name"
                    value={localSettings.fullName}
                    onChange={(e) => handleSettingChange('fullName', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={localSettings.email}
                    onChange={(e) => handleSettingChange('email', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Phone"
                    value={localSettings.phone}
                    onChange={(e) => handleSettingChange('phone', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Bar Number"
                    value={localSettings.barNumber}
                    onChange={(e) => handleSettingChange('barNumber', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Law Firm"
                    value={localSettings.lawFirm}
                    onChange={(e) => handleSettingChange('lawFirm', e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Practice Areas</InputLabel>
                    <Select
                      multiple
                      value={localSettings.practiceAreas}
                      onChange={(e) => handleSettingChange('practiceAreas', e.target.value)}
                      renderValue={(selected) => (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {(selected as string[]).map((value) => (
                            <Chip key={value} label={value} size="small" />
                          ))}
                        </Box>
                      )}
                    >
                      {practiceAreas.map((area) => (
                        <MenuItem key={area} value={area}>
                          {area}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Jurisdiction"
                    value={localSettings.jurisdiction}
                    onChange={(e) => handleSettingChange('jurisdiction', e.target.value)}
                  />
                </Grid>
              </Grid>
            </TabPanel>

            {/* AI Assistant Settings */}
            <TabPanel value={currentTab} index={1}>
              <Typography variant="h6" gutterBottom>AI Assistant Configuration</Typography>
              
              <Card sx={{ mb: 3 }}>
                <CardHeader title="Response Settings" />
                <CardContent>
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Typography gutterBottom>Temperature: {localSettings.temperature}</Typography>
                      <Slider
                        value={localSettings.temperature}
                        onChange={(_, value) => handleSettingChange('temperature', value)}
                        min={0.0}
                        max={1.0}
                        step={0.1}
                        marks={[
                          { value: 0.0, label: 'Precise' },
                          { value: 0.5, label: 'Balanced' },
                          { value: 1.0, label: 'Creative' }
                        ]}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography gutterBottom>Max Tokens: {localSettings.maxTokens}</Typography>
                      <Slider
                        value={localSettings.maxTokens}
                        onChange={(_, value) => handleSettingChange('maxTokens', value)}
                        min={1000}
                        max={8000}
                        step={500}
                        marks={[
                          { value: 1000, label: '1K' },
                          { value: 4000, label: '4K' },
                          { value: 8000, label: '8K' }
                        ]}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Response Style</InputLabel>
                        <Select
                          value={localSettings.responseStyle}
                          onChange={(e) => handleSettingChange('responseStyle', e.target.value)}
                        >
                          <MenuItem value="professional">Professional</MenuItem>
                          <MenuItem value="detailed">Detailed</MenuItem>
                          <MenuItem value="concise">Concise</MenuItem>
                          <MenuItem value="conversational">Conversational</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <FormControl fullWidth>
                        <InputLabel>Citation Style</InputLabel>
                        <Select
                          value={localSettings.citationStyle}
                          onChange={(e) => handleSettingChange('citationStyle', e.target.value)}
                        >
                          <MenuItem value="bluebook">Bluebook</MenuItem>
                          <MenuItem value="apa">APA</MenuItem>
                          <MenuItem value="mla">MLA</MenuItem>
                          <MenuItem value="chicago">Chicago</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Alert severity="info" sx={{ mb: 2 }}>
                Lower temperature values provide more consistent, factual responses ideal for legal work.
                Higher values increase creativity but may reduce accuracy.
              </Alert>
            </TabPanel>

            {/* Memory Settings */}
            <TabPanel value={currentTab} index={2}>
              <Typography variant="h6" gutterBottom>Memory & Context Management</Typography>
              
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={localSettings.memoryEnabled}
                      onChange={(e) => handleSettingChange('memoryEnabled', e.target.checked)}
                    />
                  }
                  label="Enable Memory System"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={localSettings.crossConversationContext}
                      onChange={(e) => handleSettingChange('crossConversationContext', e.target.checked)}
                    />
                  }
                  label="Cross-Conversation Context"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={localSettings.autoSummarize}
                      onChange={(e) => handleSettingChange('autoSummarize', e.target.checked)}
                    />
                  }
                  label="Auto-Summarize Conversations"
                />
              </FormGroup>

              <Box sx={{ mt: 3 }}>
                <Typography gutterBottom>Max Context Messages: {localSettings.maxContextMessages}</Typography>
                <Slider
                  value={localSettings.maxContextMessages}
                  onChange={(_, value) => handleSettingChange('maxContextMessages', value)}
                  min={10}
                  max={100}
                  step={10}
                  marks={[
                    { value: 10, label: '10' },
                    { value: 50, label: '50' },
                    { value: 100, label: '100' }
                  ]}
                />
              </Box>
            </TabPanel>

            {/* Interface Settings */}
            <TabPanel value={currentTab} index={3}>
              <Typography variant="h6" gutterBottom>Interface Preferences</Typography>
              
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Theme</InputLabel>
                    <Select
                      value={localSettings.theme}
                      onChange={(e) => handleSettingChange('theme', e.target.value)}
                    >
                      <MenuItem value="light">Light</MenuItem>
                      <MenuItem value="dark">Dark</MenuItem>
                      <MenuItem value="auto">Auto</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Language</InputLabel>
                    <Select
                      value={localSettings.language}
                      onChange={(e) => handleSettingChange('language', e.target.value)}
                    >
                      <MenuItem value="en">English</MenuItem>
                      <MenuItem value="es">Spanish</MenuItem>
                      <MenuItem value="fr">French</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={4}>
                  <FormControl fullWidth>
                    <InputLabel>Font Size</InputLabel>
                    <Select
                      value={localSettings.fontSize}
                      onChange={(e) => handleSettingChange('fontSize', e.target.value)}
                    >
                      <MenuItem value="small">Small</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="large">Large</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>

              <FormGroup sx={{ mt: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={localSettings.sidebarCollapsed}
                      onChange={(e) => handleSettingChange('sidebarCollapsed', e.target.checked)}
                    />
                  }
                  label="Collapse Sidebar by Default"
                />
              </FormGroup>
            </TabPanel>

            {/* Security Settings */}
            <TabPanel value={currentTab} index={5}>
              <Typography variant="h6" gutterBottom>Security & Privacy</Typography>
              
              <FormGroup>
                <FormControlLabel
                  control={
                    <Switch
                      checked={localSettings.twoFactorEnabled}
                      onChange={(e) => handleSettingChange('twoFactorEnabled', e.target.checked)}
                    />
                  }
                  label="Two-Factor Authentication"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={localSettings.loginRequired}
                      onChange={(e) => handleSettingChange('loginRequired', e.target.checked)}
                    />
                  }
                  label="Require Login"
                />
              </FormGroup>

              <Box sx={{ mt: 3 }}>
                <Typography gutterBottom>Session Timeout (minutes): {localSettings.sessionTimeout}</Typography>
                <Slider
                  value={localSettings.sessionTimeout}
                  onChange={(_, value) => handleSettingChange('sessionTimeout', value)}
                  min={15}
                  max={480}
                  step={15}
                  marks={[
                    { value: 15, label: '15m' },
                    { value: 60, label: '1h' },
                    { value: 240, label: '4h' },
                    { value: 480, label: '8h' }
                  ]}
                />
              </Box>
            </TabPanel>
          </DialogContent>
        </Box>
      </Box>

      <DialogActions sx={{ p: 3, borderTop: 1, borderColor: 'divider' }}>
        <Button onClick={handleReset} startIcon={<Restore />}>
          Reset to Defaults
        </Button>
        <Box sx={{ flex: 1 }} />
        <Button onClick={onClose}>
          Cancel
        </Button>
        <Button
          variant="contained"
          onClick={handleSave}
          disabled={!hasChanges}
          startIcon={<Save />}
        >
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
};
