import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  TextField,
  IconButton,
  Typography,
  Divider,
  Chip,
  Tooltip,
  Alert,
  Fade,
  CircularProgress,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import {
  Send,
  AttachFile,
  Psychology,
  AutoMode,
  Lightbulb,
  History,
  MoreVert
} from '@mui/icons-material';

import StyleSelector from './StyleSelector';
import StyleIndicator from './StyleIndicator';

interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  style_used?: {
    id: string;
    name: string;
    category: string;
  };
  confidence_score?: number;
}

interface StyleSuggestion {
  style_id: string;
  style_name: string;
  reason: string;
  confidence: number;
}

interface EnhancedChatInterfaceProps {
  conversationId: number;
  onSendMessage: (message: string, files?: File[]) => Promise<any>;
}

const EnhancedChatInterface: React.FC<EnhancedChatInterfaceProps> = ({
  conversationId,
  onSendMessage
}) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [currentStyle, setCurrentStyle] = useState('advisor');
  const [isLoading, setIsLoading] = useState(false);
  const [styleSelector, setStyleSelector] = useState(false);
  const [styleSuggestions, setStyleSuggestions] = useState<StyleSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [autoSwitchEnabled, setAutoSwitchEnabled] = useState(false);
  const [attachedFiles, setAttachedFiles] = useState<File[]>([]);
  const [menuAnchor, setMenuAnchor] = useState<null | HTMLElement>(null);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    loadUserPreferences();
    loadConversationHistory();
  }, [conversationId]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadUserPreferences = async () => {
    try {
      const response = await fetch('/api/communication-styles/user/preferences');
      const preferences = await response.json();
      setCurrentStyle(preferences.active_style_id);
      setAutoSwitchEnabled(preferences.auto_switch_enabled);
    } catch (error) {
      console.error('Error loading user preferences:', error);
    }
  };

  const loadConversationHistory = async () => {
    try {
      // Load conversation messages
      const response = await fetch(`/api/conversations/${conversationId}/messages`);
      const messagesData = await response.json();
      setMessages(messagesData);
    } catch (error) {
      console.error('Error loading conversation history:', error);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() && attachedFiles.length === 0) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsLoading(true);

    try {
      // Check for style suggestions before sending
      if (autoSwitchEnabled) {
        await checkForStyleSuggestions(inputMessage);
      }

      // Send message to backend
      const response = await onSendMessage(inputMessage, attachedFiles);
      
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: response.response,
        sender: 'ai',
        timestamp: new Date(),
        style_used: response.style_used,
        confidence_score: response.confidence_score
      };

      setMessages(prev => [...prev, aiMessage]);
      setAttachedFiles([]);

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: 'Sorry, I encountered an error processing your message. Please try again.',
        sender: 'ai',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const checkForStyleSuggestions = async (message: string) => {
    try {
      const response = await fetch('/api/communication-styles/suggest', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          current_style: currentStyle,
          conversation_id: conversationId
        }),
      });

      if (response.ok) {
        const suggestions = await response.json();
        if (suggestions.length > 0) {
          setStyleSuggestions(suggestions);
          setShowSuggestions(true);
        }
      }
    } catch (error) {
      console.error('Error getting style suggestions:', error);
    }
  };

  const handleStyleChange = (newStyleId: string, context?: string) => {
    setCurrentStyle(newStyleId);
    setShowSuggestions(false);
    setStyleSuggestions([]);
  };

  const handleFileAttach = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachedFiles(prev => [...prev, ...files]);
  };

  const removeAttachedFile = (index: number) => {
    setAttachedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const renderMessage = (message: Message) => (
    <Box
      key={message.id}
      sx={{
        display: 'flex',
        justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
        mb: 2
      }}
    >
      <Paper
        elevation={1}
        sx={{
          p: 2,
          maxWidth: '70%',
          backgroundColor: message.sender === 'user' ? 'primary.main' : 'background.paper',
          color: message.sender === 'user' ? 'primary.contrastText' : 'text.primary'
        }}
      >
        <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
          {message.content}
        </Typography>
        
        <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
          <Typography variant="caption" sx={{ opacity: 0.7 }}>
            {message.timestamp.toLocaleTimeString()}
          </Typography>
          
          {message.sender === 'ai' && message.style_used && (
            <Box display="flex" alignItems="center" gap={1}>
              <Chip
                label={message.style_used.name}
                size="small"
                variant="outlined"
                sx={{ fontSize: '0.7rem', height: 20 }}
              />
              {message.confidence_score && (
                <Tooltip title={`Confidence: ${(message.confidence_score * 100).toFixed(0)}%`}>
                  <Typography variant="caption" sx={{ opacity: 0.7 }}>
                    {(message.confidence_score * 100).toFixed(0)}%
                  </Typography>
                </Tooltip>
              )}
            </Box>
          )}
        </Box>
      </Paper>
    </Box>
  );

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Header with Style Indicator */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">Legal AI Assistant</Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <StyleIndicator
              currentStyleId={currentStyle}
              onStyleChange={handleStyleChange}
              onOpenStyleSelector={() => setStyleSelector(true)}
              conversationId={conversationId}
            />
            <IconButton
              size="small"
              onClick={(e) => setMenuAnchor(e.currentTarget)}
            >
              <MoreVert />
            </IconButton>
          </Box>
        </Box>
      </Box>

      {/* Style Suggestions */}
      <Fade in={showSuggestions}>
        <Alert
          severity="info"
          action={
            <IconButton
              size="small"
              onClick={() => setShowSuggestions(false)}
            >
              ×
            </IconButton>
          }
          sx={{ m: 1 }}
        >
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              Style Suggestion
            </Typography>
            {styleSuggestions.map((suggestion, index) => (
              <Chip
                key={index}
                label={`${suggestion.style_name}: ${suggestion.reason}`}
                onClick={() => handleStyleChange(suggestion.style_id, 'suggestion')}
                sx={{ mr: 1, mb: 1, cursor: 'pointer' }}
                icon={<Lightbulb />}
              />
            ))}
          </Box>
        </Alert>
      </Fade>

      {/* Messages Area */}
      <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
        {messages.map(renderMessage)}
        {isLoading && (
          <Box display="flex" justifyContent="flex-start" mb={2}>
            <Paper elevation={1} sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
              <CircularProgress size={16} />
              <Typography variant="body2">AI is thinking...</Typography>
            </Paper>
          </Box>
        )}
        <div ref={messagesEndRef} />
      </Box>

      {/* Attached Files */}
      {attachedFiles.length > 0 && (
        <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
          <Box display="flex" flexWrap="wrap" gap={1}>
            {attachedFiles.map((file, index) => (
              <Chip
                key={index}
                label={file.name}
                onDelete={() => removeAttachedFile(index)}
                size="small"
              />
            ))}
          </Box>
        </Box>
      )}

      {/* Input Area */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Box display="flex" alignItems="flex-end" gap={1}>
          <TextField
            fullWidth
            multiline
            maxRows={4}
            placeholder="Ask your legal question..."
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            disabled={isLoading}
          />
          <IconButton onClick={handleFileAttach} disabled={isLoading}>
            <AttachFile />
          </IconButton>
          <IconButton
            onClick={handleSendMessage}
            disabled={isLoading || (!inputMessage.trim() && attachedFiles.length === 0)}
            color="primary"
          >
            <Send />
          </IconButton>
        </Box>
      </Box>

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileSelect}
        multiple
        style={{ display: 'none' }}
        accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"
      />

      {/* Style Selector Dialog */}
      <StyleSelector
        open={styleSelector}
        onClose={() => setStyleSelector(false)}
        onStyleChange={handleStyleChange}
        currentStyle={currentStyle}
        conversationId={conversationId}
      />

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => { setStyleSelector(true); setMenuAnchor(null); }}>
          <ListItemIcon>
            <Psychology fontSize="small" />
          </ListItemIcon>
          <ListItemText>Change Communication Style</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <ListItemIcon>
            <History fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Style History</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => setMenuAnchor(null)}>
          <ListItemIcon>
            <AutoMode fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            {autoSwitchEnabled ? 'Disable' : 'Enable'} Auto-Switch
          </ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default EnhancedChatInterface;
