import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>ton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Tooltip,
  IconButton,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Switch,
  FormControlLabel,
  Alert
} from '@mui/material';
import {
  Psychology,
  Gavel,
  Business,
  Family,
  Security,
  AccountBalance,
  Person,
  Groups,
  Analytics,
  Settings,
  Star,
  StarBorder,
  History,
  AutoMode,
  Close,
  Info
} from '@mui/icons-material';

interface CommunicationStyle {
  id: string;
  name: string;
  category: string;
  description: string;
  use_cases: string[];
  tone_keywords: string[];
  example_phrases: string[];
}

interface StyleCombination {
  id: string;
  name: string;
  description: string;
  primary_style: string;
  secondary_style: string;
}

interface UserStylePreferences {
  active_style_id: string;
  preferred_styles: string[];
  context_style_mappings: Record<string, string>;
  auto_switch_enabled: boolean;
  auto_switch_rules: any[];
}

interface StyleSelectorProps {
  open: boolean;
  onClose: () => void;
  onStyleChange: (styleId: string, context?: string) => void;
  currentStyle: string;
  conversationId?: number;
}

const StyleSelector: React.FC<StyleSelectorProps> = ({
  open,
  onClose,
  onStyleChange,
  currentStyle,
  conversationId
}) => {
  const [styles, setStyles] = useState<CommunicationStyle[]>([]);
  const [styleCategories, setStyleCategories] = useState<Record<string, CommunicationStyle[]>>({});
  const [combinations, setCombinations] = useState<StyleCombination[]>([]);
  const [preferences, setPreferences] = useState<UserStylePreferences | null>(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedStyle, setSelectedStyle] = useState<string>(currentStyle);
  const [loading, setLoading] = useState(false);
  const [previewStyle, setPreviewStyle] = useState<CommunicationStyle | null>(null);

  // Category icons mapping
  const categoryIcons: Record<string, React.ReactElement> = {
    professional_role: <Psychology />,
    legal_party: <Gavel />,
    practice_area: <Business />,
    communication_tone: <Person />,
    document_type: <AccountBalance />
  };

  useEffect(() => {
    if (open) {
      loadStyleData();
    }
  }, [open]);

  const loadStyleData = async () => {
    setLoading(true);
    try {
      // Load all styles
      const stylesResponse = await fetch('/api/communication-styles/styles');
      const stylesData = await stylesResponse.json();
      setStyles(stylesData);

      // Load style categories
      const categoriesResponse = await fetch('/api/communication-styles/styles/categories');
      const categoriesData = await categoriesResponse.json();
      setStyleCategories(categoriesData);

      // Load combinations
      const combinationsResponse = await fetch('/api/communication-styles/combinations');
      const combinationsData = await combinationsResponse.json();
      setCombinations(combinationsData);

      // Load user preferences
      const preferencesResponse = await fetch('/api/communication-styles/user/preferences');
      const preferencesData = await preferencesResponse.json();
      setPreferences(preferencesData);

      setSelectedStyle(preferencesData.active_style_id);
    } catch (error) {
      console.error('Error loading style data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStyleSelect = (styleId: string) => {
    setSelectedStyle(styleId);
    const style = styles.find(s => s.id === styleId);
    setPreviewStyle(style || null);
  };

  const handleApplyStyle = async () => {
    if (selectedStyle === currentStyle) {
      onClose();
      return;
    }

    try {
      const response = await fetch('/api/communication-styles/user/change-style', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          style_id: selectedStyle,
          conversation_id: conversationId,
          context: 'user_selection',
          reason: 'Manual style selection from chat interface'
        }),
      });

      if (response.ok) {
        onStyleChange(selectedStyle, 'user_selection');
        onClose();
      }
    } catch (error) {
      console.error('Error changing style:', error);
    }
  };

  const togglePreferredStyle = async (styleId: string) => {
    if (!preferences) return;

    const isPreferred = preferences.preferred_styles.includes(styleId);
    const updatedPreferred = isPreferred
      ? preferences.preferred_styles.filter(id => id !== styleId)
      : [...preferences.preferred_styles, styleId];

    const updatedPreferences = {
      ...preferences,
      preferred_styles: updatedPreferred
    };

    try {
      await fetch('/api/communication-styles/user/preferences', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedPreferences),
      });

      setPreferences(updatedPreferences);
    } catch (error) {
      console.error('Error updating preferences:', error);
    }
  };

  const renderStyleCard = (style: CommunicationStyle) => (
    <Card
      key={style.id}
      sx={{
        cursor: 'pointer',
        border: selectedStyle === style.id ? 2 : 1,
        borderColor: selectedStyle === style.id ? 'primary.main' : 'divider',
        '&:hover': {
          borderColor: 'primary.main',
          elevation: 4
        }
      }}
      onClick={() => handleStyleSelect(style.id)}
    >
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="flex-start">
          <Box flex={1}>
            <Typography variant="h6" gutterBottom>
              {style.name}
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              {style.description}
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={0.5} mb={1}>
              {style.tone_keywords.slice(0, 3).map((keyword) => (
                <Chip
                  key={keyword}
                  label={keyword}
                  size="small"
                  variant="outlined"
                  color="primary"
                />
              ))}
            </Box>
          </Box>
          <Box display="flex" flexDirection="column" alignItems="center">
            <IconButton
              size="small"
              onClick={(e) => {
                e.stopPropagation();
                togglePreferredStyle(style.id);
              }}
            >
              {preferences?.preferred_styles.includes(style.id) ? (
                <Star color="primary" />
              ) : (
                <StarBorder />
              )}
            </IconButton>
            {currentStyle === style.id && (
              <Chip label="Active" color="success" size="small" />
            )}
          </Box>
        </Box>
      </CardContent>
      <CardActions>
        <Button
          size="small"
          startIcon={<Info />}
          onClick={(e) => {
            e.stopPropagation();
            setPreviewStyle(style);
          }}
        >
          Preview
        </Button>
      </CardActions>
    </Card>
  );

  const renderCombinationCard = (combination: StyleCombination) => (
    <Card
      key={combination.id}
      sx={{
        cursor: 'pointer',
        border: selectedStyle === combination.id ? 2 : 1,
        borderColor: selectedStyle === combination.id ? 'primary.main' : 'divider',
        '&:hover': {
          borderColor: 'primary.main',
          elevation: 4
        }
      }}
      onClick={() => handleStyleSelect(combination.id)}
    >
      <CardContent>
        <Typography variant="h6" gutterBottom>
          {combination.name}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          {combination.description}
        </Typography>
        <Box display="flex" gap={1}>
          <Chip
            label={combination.primary_style}
            size="small"
            color="primary"
            variant="filled"
          />
          <Typography variant="body2" sx={{ alignSelf: 'center' }}>+</Typography>
          <Chip
            label={combination.secondary_style}
            size="small"
            color="secondary"
            variant="outlined"
          />
        </Box>
      </CardContent>
    </Card>
  );

  const renderPreferredStyles = () => {
    if (!preferences?.preferred_styles.length) {
      return (
        <Alert severity="info">
          No preferred styles yet. Star styles you use frequently for quick access.
        </Alert>
      );
    }

    const preferredStyleObjects = styles.filter(style =>
      preferences.preferred_styles.includes(style.id)
    );

    return (
      <Grid container spacing={2}>
        {preferredStyleObjects.map(style => (
          <Grid item xs={12} md={6} key={style.id}>
            {renderStyleCard(style)}
          </Grid>
        ))}
      </Grid>
    );
  };

  const renderStylesByCategory = () => (
    <Box>
      {Object.entries(styleCategories).map(([category, categoryStyles]) => (
        <Box key={category} mb={3}>
          <Box display="flex" alignItems="center" mb={2}>
            {categoryIcons[category]}
            <Typography variant="h6" sx={{ ml: 1, textTransform: 'capitalize' }}>
              {category.replace('_', ' ')}
            </Typography>
          </Box>
          <Grid container spacing={2}>
            {categoryStyles.map(style => (
              <Grid item xs={12} md={6} key={style.id}>
                {renderStyleCard(style)}
              </Grid>
            ))}
          </Grid>
          <Divider sx={{ mt: 2 }} />
        </Box>
      ))}
    </Box>
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: { height: '80vh' }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h5">Communication Styles</Typography>
          <IconButton onClick={onClose}>
            <Close />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
          <Tabs value={selectedTab} onChange={(_, newValue) => setSelectedTab(newValue)}>
            <Tab label="Preferred" icon={<Star />} />
            <Tab label="All Styles" icon={<Psychology />} />
            <Tab label="Combinations" icon={<Groups />} />
            <Tab label="Settings" icon={<Settings />} />
          </Tabs>
        </Box>

        <Box sx={{ height: 'calc(100% - 100px)', overflow: 'auto' }}>
          {selectedTab === 0 && renderPreferredStyles()}
          {selectedTab === 1 && renderStylesByCategory()}
          {selectedTab === 2 && (
            <Grid container spacing={2}>
              {combinations.map(combination => (
                <Grid item xs={12} md={6} key={combination.id}>
                  {renderCombinationCard(combination)}
                </Grid>
              ))}
            </Grid>
          )}
          {selectedTab === 3 && (
            <Box>
              <FormControlLabel
                control={
                  <Switch
                    checked={preferences?.auto_switch_enabled || false}
                    onChange={(e) => {
                      if (preferences) {
                        const updated = { ...preferences, auto_switch_enabled: e.target.checked };
                        setPreferences(updated);
                      }
                    }}
                  />
                }
                label="Enable automatic style switching based on context"
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                When enabled, the AI will automatically switch communication styles based on the legal context and content of your conversation.
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button
          variant="contained"
          onClick={handleApplyStyle}
          disabled={selectedStyle === currentStyle}
        >
          Apply Style
        </Button>
      </DialogActions>

      {/* Style Preview Dialog */}
      <Dialog
        open={!!previewStyle}
        onClose={() => setPreviewStyle(null)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>{previewStyle?.name} Preview</DialogTitle>
        <DialogContent>
          {previewStyle && (
            <Box>
              <Typography variant="body1" paragraph>
                {previewStyle.description}
              </Typography>
              
              <Typography variant="h6" gutterBottom>Use Cases:</Typography>
              <List dense>
                {previewStyle.use_cases.map((useCase, index) => (
                  <ListItem key={index}>
                    <ListItemText primary={useCase} />
                  </ListItem>
                ))}
              </List>

              <Typography variant="h6" gutterBottom>Example Phrases:</Typography>
              <List dense>
                {previewStyle.example_phrases.slice(0, 3).map((phrase, index) => (
                  <ListItem key={index}>
                    <ListItemText 
                      primary={`"${phrase}"`}
                      sx={{ fontStyle: 'italic' }}
                    />
                  </ListItem>
                ))}
              </List>

              <Typography variant="h6" gutterBottom>Tone Keywords:</Typography>
              <Box display="flex" flexWrap="wrap" gap={1}>
                {previewStyle.tone_keywords.map((keyword) => (
                  <Chip
                    key={keyword}
                    label={keyword}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                ))}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPreviewStyle(null)}>Close</Button>
          <Button
            variant="contained"
            onClick={() => {
              if (previewStyle) {
                handleStyleSelect(previewStyle.id);
                setPreviewStyle(null);
              }
            }}
          >
            Select This Style
          </Button>
        </DialogActions>
      </Dialog>
    </Dialog>
  );
};

export default StyleSelector;
