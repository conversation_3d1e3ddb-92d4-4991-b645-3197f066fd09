import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Divider,
  Badge
} from '@mui/material';
import {
  Psychology,
  Gavel,
  Business,
  Family,
  Security,
  AccountBalance,
  Person,
  Groups,
  ExpandMore,
  Star,
  History,
  Settings,
  AutoMode
} from '@mui/icons-material';

interface CommunicationStyle {
  id: string;
  name: string;
  category: string;
  description: string;
  tone_keywords: string[];
}

interface StyleIndicatorProps {
  currentStyleId: string;
  onStyleChange: (styleId: string) => void;
  onOpenStyleSelector: () => void;
  conversationId?: number;
  compact?: boolean;
}

const StyleIndicator: React.FC<StyleIndicatorProps> = ({
  currentStyleId,
  onStyleChange,
  onOpenStyleSelector,
  conversationId,
  compact = false
}) => {
  const [currentStyle, setCurrentStyle] = useState<CommunicationStyle | null>(null);
  const [recentStyles, setRecentStyles] = useState<CommunicationStyle[]>([]);
  const [preferredStyles, setPreferredStyles] = useState<CommunicationStyle[]>([]);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [autoSwitchEnabled, setAutoSwitchEnabled] = useState(false);

  // Category icons mapping
  const categoryIcons: Record<string, React.ReactElement> = {
    professional_role: <Psychology fontSize="small" />,
    legal_party: <Gavel fontSize="small" />,
    practice_area: <Business fontSize="small" />,
    communication_tone: <Person fontSize="small" />,
    document_type: <AccountBalance fontSize="small" />
  };

  // Style color mapping based on category
  const getCategoryColor = (category: string): "primary" | "secondary" | "success" | "warning" | "info" => {
    switch (category) {
      case 'professional_role': return 'primary';
      case 'legal_party': return 'secondary';
      case 'practice_area': return 'success';
      case 'communication_tone': return 'info';
      case 'document_type': return 'warning';
      default: return 'primary';
    }
  };

  useEffect(() => {
    loadCurrentStyle();
    loadQuickAccessStyles();
  }, [currentStyleId]);

  const loadCurrentStyle = async () => {
    try {
      const response = await fetch(`/api/communication-styles/styles/${currentStyleId}`);
      if (response.ok) {
        const styleData = await response.json();
        setCurrentStyle(styleData);
      }
    } catch (error) {
      console.error('Error loading current style:', error);
    }
  };

  const loadQuickAccessStyles = async () => {
    try {
      // Load user preferences to get preferred styles and recent usage
      const preferencesResponse = await fetch('/api/communication-styles/user/preferences');
      const preferences = await preferencesResponse.json();
      setAutoSwitchEnabled(preferences.auto_switch_enabled);

      // Load analytics to get recent styles
      const analyticsResponse = await fetch('/api/communication-styles/user/analytics');
      const analytics = await analyticsResponse.json();

      // Get style details for most used styles
      const mostUsedIds = analytics.most_used_styles.slice(0, 5).map((s: any) => s.style_id);
      const preferredIds = preferences.preferred_styles;

      // Load style details
      const allStylesResponse = await fetch('/api/communication-styles/styles');
      const allStyles = await allStylesResponse.json();

      const recentStyleObjects = allStyles.filter((style: CommunicationStyle) =>
        mostUsedIds.includes(style.id) && style.id !== currentStyleId
      );

      const preferredStyleObjects = allStyles.filter((style: CommunicationStyle) =>
        preferredIds.includes(style.id) && style.id !== currentStyleId
      );

      setRecentStyles(recentStyleObjects);
      setPreferredStyles(preferredStyleObjects);
    } catch (error) {
      console.error('Error loading quick access styles:', error);
    }
  };

  const handleQuickStyleChange = async (styleId: string) => {
    try {
      const response = await fetch('/api/communication-styles/user/change-style', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          style_id: styleId,
          conversation_id: conversationId,
          context: 'quick_switch',
          reason: 'Quick style switch from indicator'
        }),
      });

      if (response.ok) {
        onStyleChange(styleId);
        setAnchorEl(null);
      }
    } catch (error) {
      console.error('Error changing style:', error);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  if (!currentStyle) {
    return null;
  }

  if (compact) {
    return (
      <Tooltip title={`Current style: ${currentStyle.name}`}>
        <Chip
          icon={categoryIcons[currentStyle.category]}
          label={currentStyle.name}
          color={getCategoryColor(currentStyle.category)}
          size="small"
          onClick={onOpenStyleSelector}
          sx={{ cursor: 'pointer' }}
        />
      </Tooltip>
    );
  }

  return (
    <Box display="flex" alignItems="center" gap={1}>
      {/* Current Style Indicator */}
      <Badge
        badgeContent={autoSwitchEnabled ? <AutoMode fontSize="small" /> : null}
        color="info"
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        <Chip
          icon={categoryIcons[currentStyle.category]}
          label={currentStyle.name}
          color={getCategoryColor(currentStyle.category)}
          variant="filled"
          onClick={onOpenStyleSelector}
          sx={{ cursor: 'pointer' }}
        />
      </Badge>

      {/* Quick Access Menu */}
      <Tooltip title="Quick style switch">
        <IconButton
          size="small"
          onClick={handleMenuOpen}
          sx={{ ml: 0.5 }}
        >
          <ExpandMore />
        </IconButton>
      </Tooltip>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: { minWidth: 280 }
        }}
      >
        {/* Preferred Styles */}
        {preferredStyles.length > 0 && (
          <>
            <MenuItem disabled>
              <ListItemIcon>
                <Star fontSize="small" />
              </ListItemIcon>
              <ListItemText>
                <Typography variant="subtitle2" color="text.secondary">
                  Preferred Styles
                </Typography>
              </ListItemText>
            </MenuItem>
            {preferredStyles.map((style) => (
              <MenuItem
                key={style.id}
                onClick={() => handleQuickStyleChange(style.id)}
              >
                <ListItemIcon>
                  {categoryIcons[style.category]}
                </ListItemIcon>
                <ListItemText>
                  <Typography variant="body2">{style.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {style.tone_keywords.slice(0, 2).join(', ')}
                  </Typography>
                </ListItemText>
              </MenuItem>
            ))}
            <Divider />
          </>
        )}

        {/* Recent Styles */}
        {recentStyles.length > 0 && (
          <>
            <MenuItem disabled>
              <ListItemIcon>
                <History fontSize="small" />
              </ListItemIcon>
              <ListItemText>
                <Typography variant="subtitle2" color="text.secondary">
                  Recently Used
                </Typography>
              </ListItemText>
            </MenuItem>
            {recentStyles.map((style) => (
              <MenuItem
                key={style.id}
                onClick={() => handleQuickStyleChange(style.id)}
              >
                <ListItemIcon>
                  {categoryIcons[style.category]}
                </ListItemIcon>
                <ListItemText>
                  <Typography variant="body2">{style.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {style.tone_keywords.slice(0, 2).join(', ')}
                  </Typography>
                </ListItemText>
              </MenuItem>
            ))}
            <Divider />
          </>
        )}

        {/* Actions */}
        <MenuItem onClick={() => { handleMenuClose(); onOpenStyleSelector(); }}>
          <ListItemIcon>
            <Settings fontSize="small" />
          </ListItemIcon>
          <ListItemText>
            <Typography variant="body2">All Styles & Settings</Typography>
          </ListItemText>
        </MenuItem>
      </Menu>

      {/* Style Description Tooltip */}
      <Tooltip
        title={
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              {currentStyle.name}
            </Typography>
            <Typography variant="body2" paragraph>
              {currentStyle.description}
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={0.5}>
              {currentStyle.tone_keywords.slice(0, 3).map((keyword) => (
                <Chip
                  key={keyword}
                  label={keyword}
                  size="small"
                  variant="outlined"
                  sx={{ 
                    fontSize: '0.7rem',
                    height: 20,
                    color: 'inherit',
                    borderColor: 'currentColor'
                  }}
                />
              ))}
            </Box>
            {autoSwitchEnabled && (
              <Typography variant="caption" color="info.main" sx={{ mt: 1, display: 'block' }}>
                Auto-switching enabled
              </Typography>
            )}
          </Box>
        }
        placement="bottom"
        arrow
      >
        <IconButton size="small" sx={{ opacity: 0.7 }}>
          <Psychology fontSize="small" />
        </IconButton>
      </Tooltip>
    </Box>
  );
};

export default StyleIndicator;
