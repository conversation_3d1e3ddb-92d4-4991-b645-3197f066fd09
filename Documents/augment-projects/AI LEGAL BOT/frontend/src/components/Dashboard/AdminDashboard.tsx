/**
 * Admin Dashboard - Comprehensive Management Interface
 * Strategic layout for deployment management and user administration
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Typography,
  Button,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  Avatar,
  Badge,
  Tooltip,
  CircularProgress,
  useTheme
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  People,
  CloudUpload,
  Analytics,
  Settings,
  Add,
  Edit,
  Delete,
  Pause,
  PlayArrow,
  Refresh,
  Download,
  Upload,
  Warning,
  CheckCircle,
  Error,
  Info,
  TrendingUp,
  TrendingDown,
  Storage,
  Memory,
  Speed,
  Public
} from '@mui/icons-material';
import {
  Line<PERSON>hart,
  Line,
  AreaChart,
  Area,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';

import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { adminService } from '../../services/adminService';

interface DashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalDeployments: number;
  activeDeployments: number;
  deploymentStats: any;
  recentDeployments: any[];
  recentUsers: any[];
  systemHealth: any;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`admin-tabpanel-${index}`}
    aria-labelledby={`admin-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
  </div>
);

const AdminDashboard: React.FC = () => {
  const theme = useTheme();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);

  // State
  const [currentTab, setCurrentTab] = useState(0);
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [provisionDialogOpen, setProvisionDialogOpen] = useState(false);
  const [bulkProvisionDialogOpen, setBulkProvisionDialogOpen] = useState(false);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const data = await adminService.getDashboard();
      setDashboardStats(data);
      setError(null);
    } catch (err: any) {
      setError(err.message || 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // Tab configuration
  const tabs = [
    { label: 'Overview', icon: <DashboardIcon /> },
    { label: 'Users', icon: <People /> },
    { label: 'Deployments', icon: <CloudUpload /> },
    { label: 'Analytics', icon: <Analytics /> },
    { label: 'System', icon: <Settings /> }
  ];

  // Status colors
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'error': return 'error';
      case 'suspended': return 'secondary';
      default: return 'default';
    }
  };

  // Health status indicator
  const HealthIndicator: React.FC<{ status: string }> = ({ status }) => {
    const color = status === 'healthy' ? 'success' : status === 'degraded' ? 'warning' : 'error';
    const icon = status === 'healthy' ? <CheckCircle /> : status === 'degraded' ? <Warning /> : <Error />;
    
    return (
      <Chip
        icon={icon}
        label={status.charAt(0).toUpperCase() + status.slice(1)}
        color={color}
        size="small"
      />
    );
  };

  // Metric card component
  const MetricCard: React.FC<{
    title: string;
    value: number | string;
    change?: number;
    icon: React.ReactNode;
    color?: string;
  }> = ({ title, value, change, icon, color = 'primary' }) => (
    <Card elevation={2}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Box>
            <Typography variant="h4" fontWeight="bold" color={color}>
              {value}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {title}
            </Typography>
            {change !== undefined && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                {change > 0 ? <TrendingUp color="success" /> : <TrendingDown color="error" />}
                <Typography
                  variant="caption"
                  color={change > 0 ? 'success.main' : 'error.main'}
                  sx={{ ml: 0.5 }}
                >
                  {change > 0 ? '+' : ''}{change}%
                </Typography>
              </Box>
            )}
          </Box>
          <Avatar sx={{ bgcolor: `${color}.main`, width: 56, height: 56 }}>
            {icon}
          </Avatar>
        </Box>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" action={
        <Button color="inherit" size="small" onClick={loadDashboardData}>
          Retry
        </Button>
      }>
        {error}
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" fontWeight="bold">
          Admin Dashboard
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadDashboardData}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => setProvisionDialogOpen(true)}
          >
            Provision User
          </Button>
        </Box>
      </Box>

      {/* System Health Alert */}
      {dashboardStats?.systemHealth?.overall !== 'healthy' && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          System health is {dashboardStats.systemHealth.overall}. Please check system status.
        </Alert>
      )}

      {/* Tabs */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={currentTab} onChange={(_, newValue) => setCurrentTab(newValue)}>
          {tabs.map((tab, index) => (
            <Tab
              key={index}
              icon={tab.icon}
              label={tab.label}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Box>

      {/* Overview Tab */}
      <TabPanel value={currentTab} index={0}>
        {/* Key Metrics */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Total Users"
              value={dashboardStats?.totalUsers || 0}
              change={12}
              icon={<People />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Active Users"
              value={dashboardStats?.activeUsers || 0}
              change={8}
              icon={<People />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Total Deployments"
              value={dashboardStats?.totalDeployments || 0}
              change={5}
              icon={<CloudUpload />}
              color="info"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <MetricCard
              title="Active Deployments"
              value={dashboardStats?.activeDeployments || 0}
              change={3}
              icon={<CloudUpload />}
              color="warning"
            />
          </Grid>
        </Grid>

        {/* Charts and Recent Activity */}
        <Grid container spacing={3}>
          {/* Deployment Status Distribution */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Deployment Status Distribution" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={Object.entries(dashboardStats?.deploymentStats?.status_distribution || {}).map(([key, value]) => ({
                        name: key,
                        value: value
                      }))}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                      label
                    >
                      {Object.entries(dashboardStats?.deploymentStats?.status_distribution || {}).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={theme.palette.primary.main} />
                      ))}
                    </Pie>
                    <RechartsTooltip />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Regional Distribution */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Regional Distribution" />
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart
                    data={Object.entries(dashboardStats?.deploymentStats?.regional_distribution || {}).map(([key, value]) => ({
                      region: key,
                      deployments: value
                    }))}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="region" />
                    <YAxis />
                    <RechartsTooltip />
                    <Bar dataKey="deployments" fill={theme.palette.secondary.main} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Deployments */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader 
                title="Recent Deployments" 
                action={
                  <Button size="small" onClick={() => setCurrentTab(2)}>
                    View All
                  </Button>
                }
              />
              <CardContent>
                <List>
                  {dashboardStats?.recentDeployments?.slice(0, 5).map((deployment, index) => (
                    <React.Fragment key={deployment.id}>
                      <ListItem>
                        <ListItemText
                          primary={deployment.tenant_name || deployment.uuid}
                          secondary={`${deployment.region} • ${new Date(deployment.created_at).toLocaleDateString()}`}
                        />
                        <ListItemSecondaryAction>
                          <Chip
                            label={deployment.status}
                            color={getStatusColor(deployment.status)}
                            size="small"
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < 4 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>

          {/* Recent Users */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader 
                title="Recent Users" 
                action={
                  <Button size="small" onClick={() => setCurrentTab(1)}>
                    View All
                  </Button>
                }
              />
              <CardContent>
                <List>
                  {dashboardStats?.recentUsers?.slice(0, 5).map((user, index) => (
                    <React.Fragment key={user.id}>
                      <ListItem>
                        <Avatar sx={{ mr: 2 }}>
                          {user.full_name?.charAt(0) || 'U'}
                        </Avatar>
                        <ListItemText
                          primary={user.full_name}
                          secondary={`${user.email} • ${new Date(user.created_at).toLocaleDateString()}`}
                        />
                        <ListItemSecondaryAction>
                          <Chip
                            label={user.subscription_tier}
                            color="primary"
                            size="small"
                            variant="outlined"
                          />
                        </ListItemSecondaryAction>
                      </ListItem>
                      {index < 4 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* System Health */}
      <TabPanel value={currentTab} index={4}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="System Health" />
              <CardContent>
                <Box sx={{ space: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="body1">Database</Typography>
                    <HealthIndicator status={dashboardStats?.systemHealth?.database || 'unknown'} />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="body1">Deployment Manager</Typography>
                    <HealthIndicator status={dashboardStats?.systemHealth?.deployment_manager || 'unknown'} />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography variant="body1">Overall Status</Typography>
                    <HealthIndicator status={dashboardStats?.systemHealth?.overall || 'unknown'} />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Resource Utilization" />
              <CardContent>
                <Box sx={{ space: 3 }}>
                  <Box>
                    <Typography variant="body2" gutterBottom>
                      CPU Utilization: {dashboardStats?.deploymentStats?.resource_utilization?.cpu_utilization || 0}%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={dashboardStats?.deploymentStats?.resource_utilization?.cpu_utilization || 0}
                      sx={{ mb: 2 }}
                    />
                  </Box>
                  <Box>
                    <Typography variant="body2" gutterBottom>
                      Memory Utilization: {dashboardStats?.deploymentStats?.resource_utilization?.memory_utilization || 0}%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={dashboardStats?.deploymentStats?.resource_utilization?.memory_utilization || 0}
                      sx={{ mb: 2 }}
                    />
                  </Box>
                  <Box>
                    <Typography variant="body2" gutterBottom>
                      Storage Utilization: {dashboardStats?.deploymentStats?.resource_utilization?.storage_utilization || 0}%
                    </Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={dashboardStats?.deploymentStats?.resource_utilization?.storage_utilization || 0}
                    />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Other tabs would be implemented similarly */}
    </Box>
  );
};

export default AdminDashboard;
