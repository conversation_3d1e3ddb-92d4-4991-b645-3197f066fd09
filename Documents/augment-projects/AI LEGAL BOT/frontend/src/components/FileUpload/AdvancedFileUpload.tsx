/**
 * Advanced File Upload Component with Chunked Upload Support
 * Handles large files with progress tracking and drag-and-drop
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  Switch,
  TextField
} from '@mui/material';
import {
  CloudUpload,
  Delete,
  Refresh,
  CheckCircle,
  Error,
  Description,
  PictureAsPdf,
  Image as ImageIcon,
  InsertDriveFile
} from '@mui/icons-material';
import { useDropzone } from 'react-dropzone';

import { fileService } from '../../services/fileService';
import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { addNotification } from '../../store/slices/appSlice';

interface FileUploadItem {
  id: string;
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  result?: any;
  chunks?: number;
  uploadedChunks?: number;
}

interface AdvancedFileUploadProps {
  onFileUploaded?: (result: any) => void;
  onFilesChanged?: (files: FileUploadItem[]) => void;
  caseId?: string;
  maxFiles?: number;
  acceptedTypes?: string[];
  autoProcess?: boolean;
}

const AdvancedFileUpload: React.FC<AdvancedFileUploadProps> = ({
  onFileUploaded,
  onFilesChanged,
  caseId,
  maxFiles = 10,
  acceptedTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/msword',
    'text/plain',
    'text/rtf',
    'image/jpeg',
    'image/png',
    'image/tiff'
  ],
  autoProcess = true
}) => {
  const dispatch = useAppDispatch();
  const [uploadItems, setUploadItems] = useState<FileUploadItem[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [processImmediately, setProcessImmediately] = useState(autoProcess);
  const [selectedCaseId, setSelectedCaseId] = useState(caseId || '');
  
  const uploadRefs = useRef<Map<string, AbortController>>(new Map());

  // File type icons
  const getFileIcon = (type: string) => {
    if (type.includes('pdf')) return <PictureAsPdf color="error" />;
    if (type.includes('word') || type.includes('document')) return <Description color="primary" />;
    if (type.includes('image')) return <ImageIcon color="secondary" />;
    return <InsertDriveFile />;
  };

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    rejectedFiles.forEach(({ file, errors }) => {
      errors.forEach((error: any) => {
        dispatch(addNotification({
          message: `File ${file.name}: ${error.message}`,
          severity: 'error'
        }));
      });
    });

    // Add accepted files
    const newItems: FileUploadItem[] = acceptedFiles.map(file => ({
      id: `${Date.now()}-${Math.random()}`,
      file,
      progress: 0,
      status: 'pending'
    }));

    setUploadItems(prev => {
      const updated = [...prev, ...newItems];
      if (updated.length > maxFiles) {
        dispatch(addNotification({
          message: `Maximum ${maxFiles} files allowed`,
          severity: 'warning'
        }));
        return updated.slice(0, maxFiles);
      }
      return updated;
    });

    // Auto-start upload if enabled
    if (autoProcess) {
      setTimeout(() => startUploads(newItems), 100);
    }
  }, [dispatch, maxFiles, autoProcess]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}),
    maxSize: 10 * 1024 * 1024 * 1024, // 10GB
    multiple: true
  });

  // Start uploads for specific items
  const startUploads = async (items: FileUploadItem[] = uploadItems) => {
    setIsUploading(true);

    for (const item of items) {
      if (item.status !== 'pending') continue;

      try {
        await uploadFile(item);
      } catch (error) {
        console.error(`Upload failed for ${item.file.name}:`, error);
      }
    }

    setIsUploading(false);
  };

  // Upload single file with chunked upload
  const uploadFile = async (item: FileUploadItem) => {
    const abortController = new AbortController();
    uploadRefs.current.set(item.id, abortController);

    try {
      // Update status
      updateItemStatus(item.id, 'uploading', 0);

      const file = item.file;
      const chunkSize = 8 * 1024 * 1024; // 8MB chunks
      const totalChunks = Math.ceil(file.size / chunkSize);

      // Start chunked upload
      const uploadResponse = await fileService.startChunkedUpload({
        filename: file.name,
        file_size: file.size,
        content_type: file.type,
        case_id: selectedCaseId || undefined,
        process_immediately: processImmediately
      });

      updateItemData(item.id, { chunks: totalChunks, uploadedChunks: 0 });

      // Upload chunks
      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        if (abortController.signal.aborted) break;

        const start = chunkIndex * chunkSize;
        const end = Math.min(start + chunkSize, file.size);
        const chunk = file.slice(start, end);

        const formData = new FormData();
        formData.append('chunk_data', chunk);
        formData.append('chunk_offset', start.toString());

        await fileService.uploadChunk(uploadResponse.file_id, formData);

        // Update progress
        const progress = ((chunkIndex + 1) / totalChunks) * 90; // Reserve 10% for processing
        updateItemStatus(item.id, 'uploading', progress);
        updateItemData(item.id, { uploadedChunks: chunkIndex + 1 });
      }

      if (!abortController.signal.aborted) {
        // Complete upload
        updateItemStatus(item.id, 'processing', 90);

        const result = await fileService.completeUpload(
          uploadResponse.file_id,
          selectedCaseId || undefined,
          processImmediately
        );

        updateItemStatus(item.id, 'completed', 100);
        updateItemData(item.id, { result });

        dispatch(addNotification({
          message: `File ${file.name} uploaded successfully`,
          severity: 'success'
        }));

        if (onFileUploaded) {
          onFileUploaded(result);
        }
      }

    } catch (error: any) {
      updateItemStatus(item.id, 'error', 0, error.message || 'Upload failed');
      dispatch(addNotification({
        message: `Upload failed for ${item.file.name}: ${error.message}`,
        severity: 'error'
      }));
    } finally {
      uploadRefs.current.delete(item.id);
    }
  };

  // Update item status
  const updateItemStatus = (id: string, status: FileUploadItem['status'], progress: number, error?: string) => {
    setUploadItems(prev => prev.map(item => 
      item.id === id ? { ...item, status, progress, error } : item
    ));
  };

  // Update item data
  const updateItemData = (id: string, data: Partial<FileUploadItem>) => {
    setUploadItems(prev => prev.map(item => 
      item.id === id ? { ...item, ...data } : item
    ));
  };

  // Remove file
  const removeFile = (id: string) => {
    // Cancel upload if in progress
    const abortController = uploadRefs.current.get(id);
    if (abortController) {
      abortController.abort();
      uploadRefs.current.delete(id);
    }

    setUploadItems(prev => prev.filter(item => item.id !== id));
  };

  // Retry upload
  const retryUpload = (id: string) => {
    const item = uploadItems.find(item => item.id === id);
    if (item) {
      updateItemStatus(id, 'pending', 0);
      uploadFile(item);
    }
  };

  // Clear completed uploads
  const clearCompleted = () => {
    setUploadItems(prev => prev.filter(item => item.status !== 'completed'));
  };

  // Effect to notify parent of changes
  useEffect(() => {
    if (onFilesChanged) {
      onFilesChanged(uploadItems);
    }
  }, [uploadItems, onFilesChanged]);

  return (
    <Box>
      {/* Upload Area */}
      <Paper
        {...getRootProps()}
        sx={{
          p: 4,
          border: '2px dashed',
          borderColor: isDragActive ? 'primary.main' : 'grey.300',
          backgroundColor: isDragActive ? 'action.hover' : 'background.paper',
          cursor: 'pointer',
          textAlign: 'center',
          transition: 'all 0.3s ease',
          '&:hover': {
            borderColor: 'primary.main',
            backgroundColor: 'action.hover'
          }
        }}
      >
        <input {...getInputProps()} />
        <CloudUpload sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          {isDragActive ? 'Drop files here' : 'Drag & drop files here, or click to select'}
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Supports PDF, Word documents, images, and text files up to 10GB
        </Typography>
        <Box sx={{ mt: 2 }}>
          <Button
            variant="outlined"
            startIcon={<CloudUpload />}
            sx={{ mr: 1 }}
          >
            Select Files
          </Button>
          <Button
            variant="text"
            onClick={(e) => {
              e.stopPropagation();
              setShowSettings(true);
            }}
          >
            Settings
          </Button>
        </Box>
      </Paper>

      {/* Upload List */}
      {uploadItems.length > 0 && (
        <Paper sx={{ mt: 2 }}>
          <Box sx={{ p: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">
              Files ({uploadItems.length})
            </Typography>
            <Box>
              {!isUploading && uploadItems.some(item => item.status === 'pending') && (
                <Button
                  variant="contained"
                  startIcon={<CloudUpload />}
                  onClick={() => startUploads()}
                  sx={{ mr: 1 }}
                >
                  Upload All
                </Button>
              )}
              <Button
                variant="text"
                onClick={clearCompleted}
                disabled={!uploadItems.some(item => item.status === 'completed')}
              >
                Clear Completed
              </Button>
            </Box>
          </Box>

          <List>
            {uploadItems.map((item) => (
              <ListItem key={item.id} divider>
                <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                  {getFileIcon(item.file.type)}
                </Box>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body1" noWrap>
                        {item.file.name}
                      </Typography>
                      <Chip
                        size="small"
                        label={item.status}
                        color={
                          item.status === 'completed' ? 'success' :
                          item.status === 'error' ? 'error' :
                          item.status === 'uploading' || item.status === 'processing' ? 'primary' :
                          'default'
                        }
                      />
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="text.secondary">
                        {formatFileSize(item.file.size)}
                        {item.chunks && ` • ${item.uploadedChunks || 0}/${item.chunks} chunks`}
                      </Typography>
                      {(item.status === 'uploading' || item.status === 'processing') && (
                        <LinearProgress
                          variant="determinate"
                          value={item.progress}
                          sx={{ mt: 1 }}
                        />
                      )}
                      {item.error && (
                        <Alert severity="error" sx={{ mt: 1 }}>
                          {item.error}
                        </Alert>
                      )}
                    </Box>
                  }
                />
                <ListItemSecondaryAction>
                  {item.status === 'error' && (
                    <IconButton onClick={() => retryUpload(item.id)} color="primary">
                      <Refresh />
                    </IconButton>
                  )}
                  {item.status === 'completed' && (
                    <IconButton color="success">
                      <CheckCircle />
                    </IconButton>
                  )}
                  <IconButton onClick={() => removeFile(item.id)} color="error">
                    <Delete />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        </Paper>
      )}

      {/* Settings Dialog */}
      <Dialog open={showSettings} onClose={() => setShowSettings(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Upload Settings</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={processImmediately}
                  onChange={(e) => setProcessImmediately(e.target.checked)}
                />
              }
              label="Process files immediately after upload"
            />
            <Typography variant="body2" color="text.secondary" sx={{ ml: 4, mb: 2 }}>
              When enabled, files will be processed for text extraction and indexing automatically
            </Typography>

            <TextField
              fullWidth
              label="Case ID (optional)"
              value={selectedCaseId}
              onChange={(e) => setSelectedCaseId(e.target.value)}
              helperText="Associate uploaded files with a specific case"
              sx={{ mb: 2 }}
            />

            <Typography variant="body2" color="text.secondary">
              <strong>Supported file types:</strong><br />
              • PDF documents<br />
              • Microsoft Word (.docx, .doc)<br />
              • Plain text (.txt)<br />
              • Rich text format (.rtf)<br />
              • Images (.jpg, .png, .tiff) - OCR enabled
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowSettings(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdvancedFileUpload;
