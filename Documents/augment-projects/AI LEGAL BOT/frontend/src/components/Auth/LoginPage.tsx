/**
 * Professional Login Page - Strategic Design
 * Secure authentication with professional branding and user management
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Link,
  Divider,
  Alert,
  InputAdornment,
  IconButton,
  FormControlLabel,
  Checkbox,
  LinearProgress,
  Chip,
  Grid,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Email,
  Lock,
  Person,
  Business,
  Gavel,
  Security,
  CheckCircle,
  Star
} from '@mui/icons-material';
import { useNavigate, useLocation } from 'react-router-dom';

import { useAppDispatch, useAppSelector } from '../../hooks/redux';
import { login, register, clearError } from '../../store/slices/authSlice';

const LoginPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const { isLoading, error, isAuthenticated } = useAppSelector(state => state.auth);

  const [mode, setMode] = useState<'login' | 'register'>('login');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
    lawFirm: '',
    barNumber: '',
    jurisdiction: '',
    practiceAreas: [] as string[],
    agreeToTerms: false
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      const from = (location.state as any)?.from?.pathname || '/dashboard';
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, navigate, location]);

  // Clear errors when switching modes
  useEffect(() => {
    dispatch(clearError());
  }, [mode, dispatch]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'login') {
      dispatch(login({
        email: formData.email,
        password: formData.password,
        rememberMe
      }));
    } else {
      if (formData.password !== formData.confirmPassword) {
        return; // Handle password mismatch
      }
      
      dispatch(register({
        email: formData.email,
        password: formData.password,
        full_name: formData.fullName,
        law_firm: formData.lawFirm,
        bar_number: formData.barNumber,
        jurisdiction: formData.jurisdiction,
        practice_areas: formData.practiceAreas
      }));
    }
  };

  // Handle input changes
  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Features showcase
  const features = [
    {
      icon: <Gavel color="primary" />,
      title: 'Legal AI Assistant',
      description: 'Advanced AI trained specifically for legal work with zero hallucination tolerance'
    },
    {
      icon: <Security color="primary" />,
      title: 'Enterprise Security',
      description: 'Bank-level encryption, secure file handling, and complete data privacy'
    },
    {
      icon: <CheckCircle color="primary" />,
      title: 'Professional Grade',
      description: 'Built for law firms and legal professionals with comprehensive features'
    }
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        background: `linear-gradient(135deg, ${theme.palette.primary.main}10 0%, ${theme.palette.secondary.main}10 100%)`,
        position: 'relative'
      }}
    >
      {/* Background pattern */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23000" fill-opacity="0.02"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
          opacity: 0.5
        }}
      />

      <Grid container sx={{ position: 'relative', zIndex: 1 }}>
        {/* Left side - Features showcase (hidden on mobile) */}
        {!isMobile && (
          <Grid item md={6} sx={{ display: 'flex', alignItems: 'center', p: 4 }}>
            <Box sx={{ maxWidth: 500, mx: 'auto' }}>
              <Typography variant="h2" fontWeight="bold" color="primary" gutterBottom>
                Legal AI Pro
              </Typography>
              <Typography variant="h5" color="text.secondary" gutterBottom>
                Professional Legal AI Assistant
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
                Revolutionize your legal practice with AI-powered assistance designed specifically 
                for legal professionals. Secure, accurate, and built for the demands of modern law.
              </Typography>

              <Box sx={{ space: 3 }}>
                {features.map((feature, index) => (
                  <Paper
                    key={index}
                    elevation={1}
                    sx={{
                      p: 3,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: 2,
                      backgroundColor: 'background.paper',
                      border: 1,
                      borderColor: 'divider'
                    }}
                  >
                    <Box sx={{ mt: 0.5 }}>{feature.icon}</Box>
                    <Box>
                      <Typography variant="h6" gutterBottom>
                        {feature.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {feature.description}
                      </Typography>
                    </Box>
                  </Paper>
                ))}
              </Box>

              <Box sx={{ mt: 4, display: 'flex', alignItems: 'center', gap: 2 }}>
                <Chip
                  icon={<Star />}
                  label="Trusted by 10,000+ Legal Professionals"
                  color="primary"
                  variant="outlined"
                />
              </Box>
            </Box>
          </Grid>
        )}

        {/* Right side - Login form */}
        <Grid 
          item 
          xs={12} 
          md={6} 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'center',
            p: 2
          }}
        >
          <Card
            elevation={8}
            sx={{
              width: '100%',
              maxWidth: 450,
              backgroundColor: 'background.paper',
              borderRadius: 3
            }}
          >
            {isLoading && <LinearProgress />}
            
            <CardContent sx={{ p: 4 }}>
              {/* Header */}
              <Box sx={{ textAlign: 'center', mb: 4 }}>
                <Typography variant="h4" fontWeight="bold" color="primary" gutterBottom>
                  {mode === 'login' ? 'Welcome Back' : 'Join Legal AI Pro'}
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {mode === 'login' 
                    ? 'Sign in to access your legal AI assistant'
                    : 'Create your professional account'
                  }
                </Typography>
              </Box>

              {/* Error alert */}
              {error && (
                <Alert severity="error" sx={{ mb: 3 }}>
                  {error}
                </Alert>
              )}

              {/* Form */}
              <form onSubmit={handleSubmit}>
                <Box sx={{ space: 2 }}>
                  {mode === 'register' && (
                    <>
                      <TextField
                        fullWidth
                        label="Full Name"
                        value={formData.fullName}
                        onChange={(e) => handleInputChange('fullName', e.target.value)}
                        required
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Person color="action" />
                            </InputAdornment>
                          )
                        }}
                        sx={{ mb: 2 }}
                      />
                      
                      <TextField
                        fullWidth
                        label="Law Firm"
                        value={formData.lawFirm}
                        onChange={(e) => handleInputChange('lawFirm', e.target.value)}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Business color="action" />
                            </InputAdornment>
                          )
                        }}
                        sx={{ mb: 2 }}
                      />

                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Bar Number"
                            value={formData.barNumber}
                            onChange={(e) => handleInputChange('barNumber', e.target.value)}
                          />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            label="Jurisdiction"
                            value={formData.jurisdiction}
                            onChange={(e) => handleInputChange('jurisdiction', e.target.value)}
                          />
                        </Grid>
                      </Grid>
                    </>
                  )}

                  <TextField
                    fullWidth
                    label="Email Address"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Email color="action" />
                        </InputAdornment>
                      )
                    }}
                    sx={{ mb: 2 }}
                  />

                  <TextField
                    fullWidth
                    label="Password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Lock color="action" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      )
                    }}
                    sx={{ mb: 2 }}
                  />

                  {mode === 'register' && (
                    <TextField
                      fullWidth
                      label="Confirm Password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      value={formData.confirmPassword}
                      onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                      required
                      error={formData.password !== formData.confirmPassword && formData.confirmPassword !== ''}
                      helperText={
                        formData.password !== formData.confirmPassword && formData.confirmPassword !== ''
                          ? 'Passwords do not match'
                          : ''
                      }
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Lock color="action" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        )
                      }}
                      sx={{ mb: 2 }}
                    />
                  )}

                  {mode === 'login' && (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={rememberMe}
                          onChange={(e) => setRememberMe(e.target.checked)}
                        />
                      }
                      label="Remember me"
                      sx={{ mb: 2 }}
                    />
                  )}

                  {mode === 'register' && (
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={formData.agreeToTerms}
                          onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}
                          required
                        />
                      }
                      label={
                        <Typography variant="body2">
                          I agree to the{' '}
                          <Link href="/terms" target="_blank">Terms of Service</Link>
                          {' '}and{' '}
                          <Link href="/privacy" target="_blank">Privacy Policy</Link>
                        </Typography>
                      }
                      sx={{ mb: 2 }}
                    />
                  )}

                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    size="large"
                    disabled={isLoading || (mode === 'register' && !formData.agreeToTerms)}
                    sx={{ mb: 2, py: 1.5 }}
                  >
                    {mode === 'login' ? 'Sign In' : 'Create Account'}
                  </Button>

                  <Divider sx={{ my: 2 }}>
                    <Typography variant="body2" color="text.secondary">
                      or
                    </Typography>
                  </Divider>

                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body2" color="text.secondary">
                      {mode === 'login' ? "Don't have an account?" : 'Already have an account?'}
                      {' '}
                      <Link
                        component="button"
                        type="button"
                        onClick={() => setMode(mode === 'login' ? 'register' : 'login')}
                        sx={{ fontWeight: 'bold' }}
                      >
                        {mode === 'login' ? 'Sign Up' : 'Sign In'}
                      </Link>
                    </Typography>
                  </Box>

                  {mode === 'login' && (
                    <Box sx={{ textAlign: 'center', mt: 2 }}>
                      <Link href="/forgot-password" variant="body2">
                        Forgot your password?
                      </Link>
                    </Box>
                  )}
                </Box>
              </form>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default LoginPage;
