/**
 * Main Application Layout - Strategic UI Design
 * Professional interface with optimized user flow and feature placement
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  AppBar,
  Toolbar,
  Typography,
  IconButton,
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Divider,
  Avatar,
  Menu,
  MenuItem,
  Badge,
  Tooltip,
  useTheme,
  useMediaQuery,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Chip,
  Alert,
  Snackbar
} from '@mui/material';
import {
  Menu as MenuIcon,
  Chat,
  Folder,
  Settings,
  Dashboard,
  CloudUpload,
  Search,
  History,
  Analytics,
  Help,
  Notifications,
  AccountCircle,
  Logout,
  Add,
  Description,
  CameraAlt,
  Mic,
  VideoCall,
  Close
} from '@mui/icons-material';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';

import { useAppSelector, useAppDispatch } from '../../hooks/redux';
import { logout, toggleSidebar, addNotification } from '../../store/slices/authSlice';
import { SettingsModal } from '../Settings/SettingsModal';
import { NotificationCenter } from '../Notifications/NotificationCenter';

const DRAWER_WIDTH = 280;

interface MainLayoutProps {
  children?: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Redux state
  const { user, isAuthenticated, sidebarOpen } = useAppSelector(state => state.auth);
  const { activeConversations, unreadCount } = useAppSelector(state => state.chat);
  const { uploadProgress } = useAppSelector(state => state.files);

  // Local state
  const [userMenuAnchor, setUserMenuAnchor] = useState<null | HTMLElement>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [notificationsOpen, setNotificationsOpen] = useState(false);
  const [speedDialOpen, setSpeedDialOpen] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);

  // Navigation items with strategic placement
  const navigationItems = [
    {
      text: 'Dashboard',
      icon: <Dashboard />,
      path: '/dashboard',
      primary: true,
      description: 'Overview and quick access'
    },
    {
      text: 'Chat',
      icon: <Chat />,
      path: '/chat',
      primary: true,
      badge: unreadCount,
      description: 'AI Legal Assistant'
    },
    {
      text: 'Cases',
      icon: <Folder />,
      path: '/cases',
      primary: true,
      description: 'Case management'
    },
    {
      text: 'Documents',
      icon: <Description />,
      path: '/documents',
      primary: true,
      description: 'File management'
    },
    {
      text: 'Search',
      icon: <Search />,
      path: '/search',
      description: 'Global search across all content'
    },
    {
      text: 'History',
      icon: <History />,
      path: '/history',
      description: 'Conversation history'
    },
    {
      text: 'Analytics',
      icon: <Analytics />,
      path: '/analytics',
      description: 'Usage insights'
    }
  ];

  // Quick actions for speed dial
  const quickActions = [
    {
      icon: <Chat />,
      name: 'New Chat',
      action: () => navigate('/chat/new')
    },
    {
      icon: <CloudUpload />,
      name: 'Upload File',
      action: () => navigate('/documents/upload')
    },
    {
      icon: <Folder />,
      name: 'New Case',
      action: () => navigate('/cases/new')
    },
    {
      icon: <Mic />,
      name: 'Voice Input',
      action: () => {/* Voice input functionality */}
    }
  ];

  // Handle navigation
  const handleNavigation = (path: string) => {
    navigate(path);
    if (isMobile) {
      dispatch(toggleSidebar());
    }
  };

  // Handle user menu
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
    handleUserMenuClose();
  };

  // Get current page title
  const getCurrentPageTitle = () => {
    const currentItem = navigationItems.find(item => item.path === location.pathname);
    return currentItem?.text || 'Legal AI Assistant';
  };

  // Check if path is active
  const isActivePath = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Sidebar content
  const sidebarContent = (
    <Box sx={{ width: DRAWER_WIDTH, height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* Logo and branding */}
      <Box sx={{ p: 3, textAlign: 'center', borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h5" fontWeight="bold" color="primary">
          Legal AI Pro
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Professional Legal Assistant
        </Typography>
      </Box>

      {/* User info */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar sx={{ width: 40, height: 40 }}>
            {user?.full_name?.charAt(0) || 'U'}
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography variant="subtitle2" noWrap>
              {user?.full_name || 'User'}
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              {user?.email}
            </Typography>
            <Chip 
              label={user?.subscription_tier || 'Professional'} 
              size="small" 
              color="primary" 
              variant="outlined"
              sx={{ mt: 0.5 }}
            />
          </Box>
        </Box>
      </Box>

      {/* Navigation */}
      <List sx={{ flex: 1, py: 1 }}>
        {navigationItems.map((item) => (
          <ListItem key={item.text} disablePadding>
            <ListItemButton
              onClick={() => handleNavigation(item.path)}
              selected={isActivePath(item.path)}
              sx={{
                mx: 1,
                mb: 0.5,
                borderRadius: 2,
                '&.Mui-selected': {
                  backgroundColor: 'primary.main',
                  color: 'primary.contrastText',
                  '& .MuiListItemIcon-root': {
                    color: 'primary.contrastText'
                  }
                }
              }}
            >
              <ListItemIcon>
                {item.badge ? (
                  <Badge badgeContent={item.badge} color="error">
                    {item.icon}
                  </Badge>
                ) : (
                  item.icon
                )}
              </ListItemIcon>
              <ListItemText 
                primary={item.text}
                secondary={!item.primary ? item.description : undefined}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>

      {/* Quick stats */}
      <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" gutterBottom>
          Quick Stats
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h6" color="primary">
              {activeConversations?.length || 0}
            </Typography>
            <Typography variant="caption">Active Chats</Typography>
          </Box>
          <Box sx={{ textAlign: 'center' }}>
            <Typography variant="h6" color="secondary">
              {uploadProgress?.length || 0}
            </Typography>
            <Typography variant="caption">Uploads</Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* App Bar */}
      <AppBar 
        position="fixed" 
        sx={{ 
          zIndex: theme.zIndex.drawer + 1,
          backgroundColor: 'background.paper',
          color: 'text.primary',
          boxShadow: 1
        }}
      >
        <Toolbar>
          <IconButton
            edge="start"
            onClick={() => dispatch(toggleSidebar())}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>

          <Typography variant="h6" sx={{ flexGrow: 1 }}>
            {getCurrentPageTitle()}
          </Typography>

          {/* Action buttons */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            {/* Global search */}
            <Tooltip title="Global Search">
              <IconButton onClick={() => navigate('/search')}>
                <Search />
              </IconButton>
            </Tooltip>

            {/* Notifications */}
            <Tooltip title="Notifications">
              <IconButton onClick={() => setNotificationsOpen(true)}>
                <Badge badgeContent={notifications.length} color="error">
                  <Notifications />
                </Badge>
              </IconButton>
            </Tooltip>

            {/* Settings */}
            <Tooltip title="Settings">
              <IconButton onClick={() => setSettingsOpen(true)}>
                <Settings />
              </IconButton>
            </Tooltip>

            {/* User menu */}
            <Tooltip title="Account">
              <IconButton onClick={handleUserMenuOpen}>
                <Avatar sx={{ width: 32, height: 32 }}>
                  {user?.full_name?.charAt(0) || 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
          </Box>
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      <Drawer
        variant={isMobile ? 'temporary' : 'persistent'}
        open={sidebarOpen}
        onClose={() => dispatch(toggleSidebar())}
        sx={{
          width: DRAWER_WIDTH,
          flexShrink: 0,
          '& .MuiDrawer-paper': {
            width: DRAWER_WIDTH,
            boxSizing: 'border-box',
            borderRight: 1,
            borderColor: 'divider'
          }
        }}
      >
        {sidebarContent}
      </Drawer>

      {/* Main content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 0,
          mt: 8,
          ml: !isMobile && sidebarOpen ? 0 : 0,
          transition: theme.transitions.create(['margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
        }}
      >
        {children || <Outlet />}
      </Box>

      {/* Speed Dial for quick actions */}
      <SpeedDial
        ariaLabel="Quick Actions"
        sx={{ position: 'fixed', bottom: 24, right: 24 }}
        icon={<SpeedDialIcon />}
        open={speedDialOpen}
        onOpen={() => setSpeedDialOpen(true)}
        onClose={() => setSpeedDialOpen(false)}
      >
        {quickActions.map((action) => (
          <SpeedDialAction
            key={action.name}
            icon={action.icon}
            tooltipTitle={action.name}
            onClick={() => {
              action.action();
              setSpeedDialOpen(false);
            }}
          />
        ))}
      </SpeedDial>

      {/* User Menu */}
      <Menu
        anchorEl={userMenuAnchor}
        open={Boolean(userMenuAnchor)}
        onClose={handleUserMenuClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => navigate('/profile')}>
          <ListItemIcon><AccountCircle /></ListItemIcon>
          Profile
        </MenuItem>
        <MenuItem onClick={() => setSettingsOpen(true)}>
          <ListItemIcon><Settings /></ListItemIcon>
          Settings
        </MenuItem>
        <MenuItem onClick={() => navigate('/help')}>
          <ListItemIcon><Help /></ListItemIcon>
          Help & Support
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleLogout}>
          <ListItemIcon><Logout /></ListItemIcon>
          Logout
        </MenuItem>
      </Menu>

      {/* Settings Modal */}
      <SettingsModal
        open={settingsOpen}
        onClose={() => setSettingsOpen(false)}
      />

      {/* Notification Center */}
      <NotificationCenter
        open={notificationsOpen}
        onClose={() => setNotificationsOpen(false)}
        notifications={notifications}
      />
    </Box>
  );
};

export default MainLayout;
