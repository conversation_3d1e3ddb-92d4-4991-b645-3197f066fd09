#!/usr/bin/env python3
"""
Legal AI Bot - Setup Script
Automated setup for development and production environments
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path
import argparse


class LegalAIBotSetup:
    """Setup manager for Legal AI Bot"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "backend"
        self.frontend_dir = self.project_root / "frontend"
        self.python_version = f"{sys.version_info.major}.{sys.version_info.minor}"
        
    def check_requirements(self):
        """Check system requirements"""
        print("🔍 Checking system requirements...")
        
        # Check Python version
        if sys.version_info < (3, 9):
            print("❌ Python 3.9+ is required")
            return False
            
        # Check Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            node_version = result.stdout.strip()
            print(f"✅ Node.js: {node_version}")
        except FileNotFoundError:
            print("❌ Node.js is required. Please install Node.js 16+")
            return False
            
        # Check npm
        try:
            result = subprocess.run(["npm", "--version"], capture_output=True, text=True)
            npm_version = result.stdout.strip()
            print(f"✅ npm: {npm_version}")
        except FileNotFoundError:
            print("❌ npm is required")
            return False
            
        # Check PostgreSQL
        try:
            result = subprocess.run(["psql", "--version"], capture_output=True, text=True)
            pg_version = result.stdout.strip()
            print(f"✅ PostgreSQL: {pg_version}")
        except FileNotFoundError:
            print("⚠️  PostgreSQL not found. Please install PostgreSQL 13+")
            
        print(f"✅ Python: {self.python_version}")
        print(f"✅ Platform: {platform.system()} {platform.release()}")
        
        return True
    
    def setup_python_environment(self):
        """Setup Python virtual environment and dependencies"""
        print("\n🐍 Setting up Python environment...")
        
        # Create virtual environment
        venv_path = self.project_root / "venv"
        if not venv_path.exists():
            print("Creating virtual environment...")
            subprocess.run([sys.executable, "-m", "venv", str(venv_path)], check=True)
        
        # Determine pip path
        if platform.system() == "Windows":
            pip_path = venv_path / "Scripts" / "pip"
            python_path = venv_path / "Scripts" / "python"
        else:
            pip_path = venv_path / "bin" / "pip"
            python_path = venv_path / "bin" / "python"
        
        # Upgrade pip
        print("Upgrading pip...")
        subprocess.run([str(python_path), "-m", "pip", "install", "--upgrade", "pip"], check=True)
        
        # Install Python dependencies
        print("Installing Python dependencies...")
        requirements_file = self.project_root / "requirements.txt"
        subprocess.run([str(pip_path), "install", "-r", str(requirements_file)], check=True)
        
        print("✅ Python environment setup complete")
        
    def setup_node_environment(self):
        """Setup Node.js environment and dependencies"""
        print("\n📦 Setting up Node.js environment...")
        
        os.chdir(self.frontend_dir)
        
        # Install Node.js dependencies
        print("Installing Node.js dependencies...")
        subprocess.run(["npm", "install"], check=True)
        
        # Install Electron dependencies
        print("Installing Electron dependencies...")
        subprocess.run(["npm", "run", "postinstall"], check=True)
        
        os.chdir(self.project_root)
        print("✅ Node.js environment setup complete")
        
    def setup_database(self):
        """Setup PostgreSQL database"""
        print("\n🗄️  Setting up database...")
        
        # Check if .env file exists
        env_file = self.project_root / ".env"
        env_example = self.project_root / ".env.example"
        
        if not env_file.exists() and env_example.exists():
            print("Creating .env file from template...")
            shutil.copy(env_example, env_file)
            print("⚠️  Please edit .env file with your database credentials")
        
        # Create database directories
        db_dirs = ["database/migrations", "database/seeds", "database/schemas"]
        for db_dir in db_dirs:
            (self.project_root / db_dir).mkdir(parents=True, exist_ok=True)
        
        print("✅ Database setup prepared")
        
    def setup_directories(self):
        """Create necessary directories"""
        print("\n📁 Creating project directories...")
        
        directories = [
            "uploads",
            "logs",
            "vector_db",
            "backups",
            "temp",
            "frontend/build",
            "frontend/dist"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            
        print("✅ Project directories created")
        
    def create_startup_scripts(self):
        """Create startup scripts for development"""
        print("\n🚀 Creating startup scripts...")
        
        # Backend startup script
        if platform.system() == "Windows":
            backend_script = self.project_root / "start_backend.bat"
            backend_content = """@echo off
cd /d "%~dp0"
call venv\\Scripts\\activate
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
pause
"""
        else:
            backend_script = self.project_root / "start_backend.sh"
            backend_content = """#!/bin/bash
cd "$(dirname "$0")"
source venv/bin/activate
cd backend
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
"""
            
        with open(backend_script, 'w') as f:
            f.write(backend_content)
            
        if platform.system() != "Windows":
            os.chmod(backend_script, 0o755)
        
        # Frontend startup script
        if platform.system() == "Windows":
            frontend_script = self.project_root / "start_frontend.bat"
            frontend_content = """@echo off
cd /d "%~dp0\\frontend"
npm run electron-dev
pause
"""
        else:
            frontend_script = self.project_root / "start_frontend.sh"
            frontend_content = """#!/bin/bash
cd "$(dirname "$0")/frontend"
npm run electron-dev
"""
            
        with open(frontend_script, 'w') as f:
            f.write(frontend_content)
            
        if platform.system() != "Windows":
            os.chmod(frontend_script, 0o755)
        
        print("✅ Startup scripts created")
        
    def run_setup(self, skip_deps=False):
        """Run complete setup process"""
        print("🏗️  Legal AI Bot - Setup Starting...")
        print("=" * 50)
        
        if not self.check_requirements():
            print("❌ Setup failed: Missing requirements")
            return False
            
        try:
            self.setup_directories()
            
            if not skip_deps:
                self.setup_python_environment()
                self.setup_node_environment()
                
            self.setup_database()
            self.create_startup_scripts()
            
            print("\n" + "=" * 50)
            print("🎉 Setup completed successfully!")
            print("\nNext steps:")
            print("1. Edit .env file with your configuration")
            print("2. Set up PostgreSQL database")
            print("3. Get your DeepSeek API key")
            print("4. Run the backend: ./start_backend.sh (or .bat on Windows)")
            print("5. Run the frontend: ./start_frontend.sh (or .bat on Windows)")
            print("\nFor detailed instructions, see README.md")
            
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {str(e)}")
            return False


def main():
    """Main setup function"""
    parser = argparse.ArgumentParser(description="Legal AI Bot Setup")
    parser.add_argument("--skip-deps", action="store_true", 
                       help="Skip dependency installation")
    parser.add_argument("--dev", action="store_true",
                       help="Development setup")
    
    args = parser.parse_args()
    
    setup = LegalAIBotSetup()
    success = setup.run_setup(skip_deps=args.skip_deps)
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
