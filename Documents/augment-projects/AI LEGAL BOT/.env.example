# Legal AI Bot - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# Application Settings
DEBUG=false
HOST=localhost
PORT=8000
RELOAD=false

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/legal_ai_bot
DATABASE_ECHO=false

# DeepSeek API Configuration
DEEPSEEK_API_KEY=your_deepseek_api_key_here
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat
DEEPSEEK_MAX_TOKENS=4000
DEEPSEEK_TEMPERATURE=0.1

# Security Settings
SECRET_KEY=your_very_long_secret_key_at_least_32_characters_long
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload Settings
MAX_FILE_SIZE=104857600  # 100MB in bytes
UPLOAD_DIR=uploads

# Legal Features
ENABLE_CITATION_VALIDATION=true
LEGAL_DATABASES=westlaw,lexis,google_scholar

# Memory and Context
MAX_CONVERSATION_HISTORY=10000
CONTEXT_WINDOW_SIZE=8000

# Vector Database
VECTOR_DB_PATH=./vector_db
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Logging
LOG_LEVEL=INFO
LOG_FILE=legal_ai_bot.log

# CORS Settings
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Legal Compliance
AUDIT_LOG_ENABLED=true
DATA_RETENTION_DAYS=2555  # 7 years
ENCRYPTION_ENABLED=true

# Optional: External API Keys for Legal Research
WESTLAW_API_KEY=your_westlaw_api_key_here
LEXIS_API_KEY=your_lexis_api_key_here
GOOGLE_SCHOLAR_API_KEY=your_google_scholar_api_key_here

# Optional: OCR Service Configuration
TESSERACT_PATH=/usr/bin/tesseract
OCR_LANGUAGES=eng

# Optional: Email Configuration for Notifications
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>

# Optional: Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
